export const menu = [
  {
    alwaysShow: false,
    component: "Layout",
    hidden: true,  // 设置为true隐藏菜单
    meta: {
      title: "Dify",
      titleEn: "component Demo",
      icon: "icon-xinhuihua",
      noCache: true,
      link: "",
      activeMenu: "",
    },
    name: "difyIframe",
    path: "/dify",
    children: [
      {
        component: "difyIframe/index",
        hidden: false,
        meta: {
          title: "新会话页",
          titleEn: "New Chat",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "NewChatPage",
        path: "/chat/new",
        query: "",
        pid: "1698621081462707497",
        id: "1698621081462706324",
      }
    ],
    query: "",
    pid: "",
  },
  {
    alwaysShow: false,
    component: "Layout",
    hidden: false,
    meta: {
      title: "PortalHome",
      titleEn: "PortalHome",
      icon: "icon-xinhuihua",
      noCache: true,
      link: "",
      activeMenu: "",
    },
    name: "<PERSON>Home",
    path: "/home",
    children: [
      {
        component: "home/index",
        hidden: false,
        meta: {
          title: "新会话",
          titleEn: "New Chat",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
          refreshOnSameRoute: true
        },
        name: "HomePage",
        path: "/home",
        query: "",
        pid: "1698621081462707498",
        id: "1698621081462706325",
      },
    ],
    query: "",
    pid: "",
  },
  {
    alwaysShow: false,
    component: "Layout",
    hidden: true,
    meta: {
      title: "Dify",
      titleEn: "component Demo",
      icon: "ca",
      noCache: true,
      link: "",
      activeMenu: "",
    },
    name: "difyIframe",
    path: "/app2",
    children: [
      {
        component: "difyIframe/new",
        hidden: false,
        meta: {
          title: "历史会话",
          titleEn: "History Chat",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "HistoryChatPage",
        path: "/chat/history",
        query: "",
        pid: "1698621081462707497",
        id: "1698621081462706324",
      }
    ],
    query: "",
    pid: "",
  },
  {
    alwaysShow: false,
    component: "Layout",
    hidden: false,
    meta: {
      title: "应用",
      titleEn: "component Demo",
      icon: "app",
      noCache: true,
      link: "",
      activeMenu: "",
    },
    name: "component",
    path: "/app",
    children: [
      {
        component: "application/app/index",
        hidden: false,
        meta: {
          title: "应用探索",
          titleEn: "Application Discovery",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "apps",
        path: "/app",
        query: "",
        pid: "1698621081462707497",
        id: "1698621081462706324",
      },
    ],
    query: "",
    pid: "",
  },
  {
    alwaysShow: false,
    component: "Layout",
    hidden: false,
    meta: {
      title: "数字人",
      titleEn: "Digital Assistant",
      icon: "Digital",
      noCache: true,
      link: "",
      activeMenu: "",
    },
    name: "component",
    path: "/digital-assistant",
    children: [
      {
        component: "application/digital-assistant/index",
        hidden: false,
        meta: {
          title: "数字人助手",
          titleEn: "Digital Assistant",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "DigitalPage",
        path: "/digital-assistant",
        query: "",
        pid: "1698621081462707497",
        id: "1698621081462706324",
      },
    ],
    query: "",
    pid: "",
  },
  {
    alwaysShow: false,
    component: "Layout",
    hidden: false,
    meta: {
      title: "PDF沉浸式翻译",
      titleEn: "PDF Translate",
      icon: "BabelDoc",
      noCache: true,
      link: "",
      activeMenu: "",
    },
    name: "component",
    path: "/pdftrans",
    children: [
      {
        component: "application/pdf-translate/index",
        hidden: false,
        meta: {
          title: "PDF沉浸式翻译",
          titleEn: "PDF Translate",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "pdftrans",
        path: "/pdftrans",
        query: "",
        pid: "1698621081462707497",
        id: "1698621081462706324",
      },
    ],
    query: "",
    pid: "",
  },
  {
    alwaysShow: false,
    component: "Layout",
    hidden: false,
    meta: {
      title: "MinerU文档解析器",
      titleEn: "document conversion",
      icon: "MinerU",
      noCache: true,
      link: "",
      activeMenu: "",
    },
    name: "component",
    path: "/mineru",
    children: [
      {
        component: "application/mineru/index",
        hidden: false,
        meta: {
          title: "MinerU文档解析器",
          titleEn: "document conversion",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "demo",
        path: "/mineru",
        query: "",
        pid: "1698621081462707497",
        id: "1698621081462706324",
      },
    ],
    query: "",
    pid: "",
  }
];
