// ESlint 检查配置
module.exports = {
  root: true,
  parserOptions: {
    parser: "@babel/eslint-parser",
    requireConfigFile: false, // 如果没有 Babel 配置文件
    ecmaVersion: 2020,
    sourceType: "module",
  },
  plugins: [
    "import", // 添加这个
  ],
  // 其他 ESLint 配置 识别@别名，否则找不到@路径的import
  settings: {
    "import/resolver": {
      alias: {
        map: [["@", "./src"]],
        // 引用的时候可以忽略后缀
        extensions: [".vue", ".js", ".ts", ".tsx", ".jsx", ".json"],
      },
    },
  },
  env: {
    browser: true,
    node: true,
    es6: true,
  },
  extends: ["plugin:vue/recommended", "eslint:recommended"],
  //全局忽略变量
  globals: {
    window: "readonly",
    SM2: "readonly",
    Hex: "readonly",
    swGlobal: "readonly",
    str: "readonly",
    pt_logo: "readonly",
    ptName: "readonly",
    swConsole: "readonly",
  },
  // add your custom rules here
  //it is base on https://github.com/vuejs/eslint-config-vue
  rules: {
    "import/named": "error",
    "import/default": "error",
    "import/namespace": "off",
    "import/export": "error",
    "import/no-unresolved": "error",
    // "import/no-unused-modules": [2, { unusedExports: true }],
    "import/no-unused-modules": "off",
    "vue/attribute-hyphenation": "off",
    "vue/max-attributes-per-line": [
      0,
      {
        singleline: 10,
        multiline: {
          max: 1,
          allowFirstLine: false,
        },
      },
    ],
    "vue/singleline-html-element-content-newline": "off",
    "vue/multiline-html-element-content-newline": "off",
    // "vue/name-property-casing": ["error", "PascalCase"],
    "vue/no-v-html": "off",
    "vue/html-indent": "off",
    "vue/html-closing-bracket-newline": "off",
    "vue/html-self-closing": "off",
    "vue/attributes-order": "off",
    "vue/name-property-casing": "off",
    "vue/component-definition-name-casing": "off",
    "accessor-pairs": 2,
    "arrow-spacing": [
      2,
      {
        before: true,
        after: true,
      },
    ],
    "block-spacing": [2, "always"],
    "brace-style": [
      2,
      "1tbs",
      {
        allowSingleLine: true,
      },
    ],
    camelcase: [
      0,
      {
        properties: "always",
      },
    ],
    "comma-dangle": [0, "always"],
    "comma-spacing": [
      0,
      {
        before: false,
        after: false,
      },
    ],
    "comma-style": [2, "last"],
    "constructor-super": 2,
    curly: [2, "multi-line"],
    "dot-location": [2, "property"],
    "eol-last": 2,
    // eqeqeq: ["error", "always", { null: "ignore" }],
    eqeqeq: [0, "always", { null: "ignore" }],
    "generator-star-spacing": [
      2,
      {
        before: true,
        after: true,
      },
    ],
    "handle-callback-err": [2, "^(err|error)$"],
    indent: [
      2,
      2,
      {
        SwitchCase: 1,
        ignoredNodes: ["ConditionalExpression"],
      },
    ],
    "jsx-quotes": [2, "prefer-single"],
    "key-spacing": [
      2,
      {
        beforeColon: false,
        afterColon: true,
      },
    ],
    "keyword-spacing": [
      2,
      {
        before: true,
        after: true,
      },
    ],
    "new-cap": [
      2,
      {
        newIsCap: true,
        capIsNew: false,
      },
    ],
    "new-parens": 2,
    "no-array-constructor": 2,
    "no-caller": 2,
    "no-console": "off",
    "no-class-assign": 2,
    "no-cond-assign": 2,
    "no-const-assign": 2,
    "no-control-regex": 0,
    "no-delete-var": 2,
    "no-dupe-args": 2,
    "no-dupe-class-members": 2,
    "no-dupe-keys": 2,
    "no-duplicate-case": 2,
    "no-empty-character-class": 2,
    "no-empty-pattern": 2,
    "no-eval": 2,
    "no-ex-assign": 2,
    "no-extend-native": 2,
    "no-extra-bind": 2,
    "no-extra-boolean-cast": 1,
    "no-extra-parens": [2, "functions"],
    "no-fallthrough": 2,
    "no-floating-decimal": 2,
    "no-func-assign": 2,
    "no-implied-eval": 2,
    "no-inner-declarations": [2, "functions"],
    "no-invalid-regexp": 2,
    "no-irregular-whitespace": 2,
    "no-iterator": 2,
    "no-label-var": 2,
    "no-labels": [
      2,
      {
        allowLoop: false,
        allowSwitch: false,
      },
    ],
    "no-lone-blocks": 2,
    "no-mixed-spaces-and-tabs": 2,
    "no-multi-spaces": 2,
    "no-multi-str": 2,
    "no-multiple-empty-lines": [
      2,
      {
        max: 1,
      },
    ],
    "no-native-reassign": 2,
    "no-negated-in-lhs": 2,
    "no-new-object": 2,
    "no-new-require": 2,
    "no-new-symbol": 2,
    "no-new-wrappers": 2,
    "no-obj-calls": 2,
    "no-octal": 2,
    "no-octal-escape": 2,
    "no-path-concat": 2,
    "no-proto": 2,
    "no-redeclare": 2,
    "no-regex-spaces": 2,
    "no-return-assign": [2, "except-parens"],
    "no-self-assign": 2,
    "no-self-compare": 2,
    "no-sequences": 2,
    "no-shadow-restricted-names": 2,
    "no-spaced-func": 2,
    "no-sparse-arrays": 2,
    "no-this-before-super": 2,
    "no-throw-literal": 2,
    "no-trailing-spaces": 2,
    "no-undef": 2,
    "no-undef-init": 2,
    "no-unexpected-multiline": 2,
    "no-unmodified-loop-condition": 2,
    "no-unneeded-ternary": [
      2,
      {
        defaultAssignment: false,
      },
    ],
    "no-unreachable": 2,
    "no-unsafe-finally": 2,
    "no-unused-vars": [
      2,
      {
        vars: "all",
        args: "none",
      },
    ],
    "no-useless-call": 2,
    "no-useless-computed-key": 2,
    "no-useless-constructor": 2,
    "no-useless-escape": 0,
    "no-whitespace-before-property": 2,
    "no-with": 2,
    // "one-var": [
    //   2,
    //   {
    //     initialized: "never",
    //   },
    // ],
    "one-var": "off",
    "operator-linebreak": [
      2,
      "after",
      {
        overrides: {
          "?": "before",
          ":": "before",
        },
      },
    ],
    "padded-blocks": [2, "never"],
    quotes: [
      2,
      "double",
      {
        avoidEscape: true,
        allowTemplateLiterals: true,
      },
    ],
    semi: [2, "always"],
    "semi-spacing": [
      2,
      {
        before: false,
        after: true,
      },
    ],
    "space-before-blocks": [2, "always"],
    "space-before-function-paren": [
      2,
      { anonymous: "always", named: "never", asyncArrow: "always" },
    ],
    "space-in-parens": [2, "never"],
    "space-infix-ops": 2,
    "space-unary-ops": [
      2,
      {
        words: true,
        nonwords: false,
      },
    ],
    "spaced-comment": [
      0,
      "always",
      {
        markers: [
          "global",
          "globals",
          "eslint",
          "eslint-disable",
          "*package",
          "!",
          ",",
        ],
      },
    ],
    "template-curly-spacing": [2, "never"],
    "use-isnan": 2,
    "valid-typeof": 2,
    "wrap-iife": [2, "any"],
    "yield-star-spacing": [2, "both"],
    yoda: [2, "never"],
    "prefer-const": 0,
    "no-debugger": process.env.NODE_ENV === "production" ? 2 : 0,
    // "object-curly-spacing": [
    //   2,
    //   "always",
    //   {
    //     objectsInObjects: false,
    //   },
    // ],
    "object-curly-spacing": "off",
    "array-bracket-spacing": [2, "never"],
  },
};
