## 开发

```bash
# 克隆项目


# 进入项目目录
cd sansec-plat-web

# 安装依赖
npm install

# 建议不要直接使用 cnpm 安装依赖，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npmmirror.com

# 启动服务
npm run dev
```

浏览器访问 http://localhost:80

## 发布

```bash
# 构建测试环境
npm run build:stage

# 构建生产环境
npm run build:prod

# 构建生产环境kms
npm run build:kms

# 构建生产环境 多路径 支持传入一级前缀和次级前缀 不传默认pkiweb portal
# 打包文件名yyy，'/yyy/sansecplat'，'/yyy/xxx/',yyy_secToken
npm run build:cloudPlat --first=yyy --second=xxx

npm run build:cloudPlat                        # 加解密pkiweb
npm run build:cloudPlat --first=tsaweb         # 时间戳
npm run build:cloudPlat --first=secdbhsmweb    # 数据库加密
npm run build:cloudPlat --first=smsweb         # 协签
npm run build:cloudPlat --first=secauthweb     # 统一身份认证系统
npm run build:cloudPlat --first=svsweb         # 签名
npm run build:cloudPlat --first=digestweb      # 杂凑
npm run build:cloudPlat --first=vpnweb         # ssl加密
npm run build:cloudPlat --first=tscweb         # 电子签章

# kms对接云平台
npm run build:cloudPlat:kms --first=SecKMS
npm run build:cloudPlat:kms --first=SecStorage #存储加密服务
npm run build:cloudPlat:kms --first=pkiweb  #加解密服务
npm run build:cloudPlat:kms --first=svsweb  #签名验签服务

```
