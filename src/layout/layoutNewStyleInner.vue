<template>
  <div
    id="newInnerStyle"
    :class="classObj"
    class="app-wrapper"
    :style="{ '--current-color': theme }"
  >
    <app-main />
  </div>
</template>

<script>
// import RightPanel from "@/components/RightPanel";
import { AppMain, Navbar, Sidebar, TagsView } from "./components";
import ResizeMixin from "./mixin/ResizeHandler";
import { mapState } from "vuex";
import variables from "@/assets/styles/variables.scss";
// import { getSession } from "@/utils/session";
// import { getToken } from "@/utils/auth";

export default {
  name: "LayoutNewStyleInner",
  components: {
    AppMain,
    Navbar,
    // RightPanel,
    Sidebar,
    TagsView,
  },
  mixins: [ResizeMixin],
  data() {
    return {};
  },
  computed: {
    ...mapState({
      theme: (state) => state.settings.theme,
      sideTheme: (state) => state.settings.sideTheme,
      sidebar: (state) => state.app.sidebar,
      device: (state) => state.app.device,
      needTagsView: (state) => state.settings.tagsView,
      fixedHeader: (state) => state.settings.fixedHeader,
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === "mobile",
      };
    },
    variables() {
      return variables;
    },
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch("app/closeSideBar", { withoutAnimation: false });
    },
    // 全局postMessage事件
    // messageEvt(event) {
    //   // token 和 withoutDownload 目前用于下载请求 分别表示 接收下载状态的凭证 和 是否不需要直接下载
    //   const {
    //     type,
    //     path,
    //     label,
    //     query,
    //     token,
    //     withoutDownload = false,
    //     alert,
    //     initSuccData,
    //     dialogValue,
    //     initValue,
    //     loadingData,
    //     reLoginType,
    //   } = event.data;

    //   const { oldPath, newPath } = event.data;

    //   if (type === "openNewView") {
    //     this.$router.push({
    //       path,
    //       meta: { title: label },
    //       query: { ...query, title: label },
    //     });
    //   } else if (type === "openNewIFrame") {
    //     this.$router.push({
    //       path: `/system/page/iframe/url=${encodeURIComponent(path)}`,
    //       meta: { title: "用户管理" },
    //     });
    //   } else if (type === "openNewWindow") {
    //     const otherWindow = window.open();
    //     otherWindow.opener = null;
    //     otherWindow.location = path;
    //   } else if (type === "closeTag") {
    //     window.$eventBus.$emit("closeTag", {
    //       path: `/common/page__iframeUrl=${encodeURIComponent(
    //         path,
    //       )}&title=${label}`,
    //     });
    //   } else if (type === "closeCurrentTag") {
    //     window.$eventBus.$emit("closeCurrentTag");
    //   } else if (type === "replaceTagView") {
    //     window.$eventBus.$emit("replaceCurrentTag", {
    //       oldView: { path: oldPath },
    //       newView: { path: newPath, fullPath: newPath, title: label, query },
    //     });
    //   } else if (type === "toggleScreenFull") {
    //     if (document.body.classList.contains("fullscreen-main")) {
    //       document.body.classList.remove("fullscreen-main");
    //     } else {
    //       document.body.classList.add("fullscreen-main");
    //     }
    //   } else if (type === "sessionTimeout") {
    //     window.$eventBus.$emit("sessionTimeout");
    //   } else if (type === "download") {
    //     window.$eventBus.$emit("download", {
    //       path,
    //       withoutDownload,
    //     });
    //   } else if (type === "downloadResponse") {
    //     // 本处做测试用，需要各个业务线集成响应返回数据
    //     console.log("响应下载结果", event.data);
    //   } else if (type === "loginUser") {
    //     window.$eventBus.$emit("loginUser", {
    //       token,
    //     });
    //   } else if (type === "alert") {
    //     this.$message({
    //       message: alert.message || "",
    //       duration: alert.duration || 2000,
    //       type: alert.type || "info",
    //     });
    //   } else if (type === "initSuccData") {
    //     window.$eventBus.$emit("initSuccData", {
    //       initSuccData,
    //     });
    //   } else if (type === "dialog") {
    //     window.$eventBus.$emit("dialog", {
    //       dialogValue,
    //     });
    //   } else if (type === "LicenceSucc") {
    //     window.$eventBus.$emit("LicenceSucc");
    //   } else if (type === "reLogin") {
    //     let isUk = getSession("isUkLogin");
    //     let msg = this.$t("common.reLoginTip");
    //     if (isUk && reLoginType) {
    //       if (reLoginType == 1) {
    //         // reLoginType: 1：未下载uk控件 2： 未插入uk  3：更换uk
    //         msg = this.$t("common.ukControl");
    //       } else if (reLoginType == 2) {
    //         msg = this.$t("common.unInsetUk");
    //       } else if (reLoginType == 3) {
    //         msg = this.$t("common.updateUk");
    //       }
    //     }
    //     this.confirmMessage(
    //       msg,
    //       "warning",
    //       false,
    //       false,
    //       false,
    //       this.$t("common.reLogin"),
    //       this.$t("common.sysTip"),
    //       false,
    //     )
    //       .then(() => {
    //         let url = this.getLogoutURL();
    //         if (getToken()) {
    //           this.$store.dispatch("LogOut").then((res) => {
    //             if (url) {
    //               this.$store.dispatch("app/setGlobalLoading", true);
    //               location.href = url;
    //             } else {
    //               location.href = `${this.getBasePrefix()}login`;
    //             }
    //           });
    //         } else {
    //           this.$store.dispatch("FedLogOut").then((res) => {
    //             if (url) {
    //               this.$store.dispatch("app/setGlobalLoading", true);
    //               location.href = url;
    //             } else {
    //               location.href = `${this.getBasePrefix()}login`;
    //             }
    //           });
    //         }
    //       })
    //       .catch(() => {});
    //   } else if (type === "fullLoad") {
    //     // 全局loading
    //     let loading = this.$loading({
    //       lock: true,
    //       background: loadingData?.background || "hsla(0,0%,100%,.9)",
    //       text: loadingData?.text || "Loading",
    //     });
    //     if (!loadingData.show) {
    //       loading.close();
    //     }
    //     window.$eventBus.$emit("fullIframeFixed", {
    //       fullFixed: loadingData.fullFixed,
    //     });
    //   } else if (type == "downloadUkControl") {
    //     // 下载uk控件
    //     this.confirmMessage(this.$t("initLogin.ukTip"))
    //       .then(() => {
    //         window.location.href = `${this.getBasePrefix(
    //           true,
    //         )}safe-usbkey_3.1.1.exe`;
    //       })
    //       .catch(() => {});
    //   }
    // },
  },
  // mounted() {
  //   window.addEventListener("message", this.messageEvt);
  //   // 调用消息通知接口，取出未读的列表
  //   // this.getUnReadMessage();
  // },

  // beforeDestroy() {
  //   window.removeEventListener("message", this.messageEvt);
  // },
};
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/mixin.scss";
@import "~@/assets/styles/variables.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$base-sidebar-width});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.sidebarHide .fixed-header {
  width: 100%;
}

.mobile .fixed-header {
  width: 100%;
}
</style>
<style lang="scss">
#newInnerStyle {
  .app-main {
    background-color: #f5f7fa;
    height: calc(100vh - 10px);
  }
  //列表
  .search-list-content {
    .search-table__header {
      padding: 12px 16px 0px 16px;
    }
    .content-box {
      box-shadow: none;
      .table-content {
        background: none;
        padding: 0px;
      }
    }
    .search-table__footer {
      background: none;
    }
  }
  .search-table-content {
    display: block;
  }
  //搜索条
  .search-form {
    .el-container {
      display: block;
      .el-main {
        display: inline-block;
        max-width: calc(100% - 190px);
      }
      .el-aside {
        display: inline-block;
        vertical-align: top;
      }
    }
  }
  //详情
  .s-detail {
    .detail-header {
      background: none;
      margin-bottom: 0px;
      box-shadow: none;
    }
    .detail-container {
      position: static;
    }
  }
  // .el-card {
  //   box-shadow: none;
  //   border: none;
  // }
  .el-pagination {
    button {
      border: 1px solid #d0d2d4;
      // border: 1px solid var(--color-primary);
      border-radius: 2px;
    }
    .el-pager {
      margin-left: 5px;
      li {
        border: 1px solid #d0d2d4;
        border-radius: 2px;
        margin-right: 5px;
        &.active {
          border: 1px solid var(--color-primary);
        }
      }
    }
  }
  //   .el-pagination button:hover {
  //     color: #409EFF
  // }

  // .el-pagination__sizes .el-input .el-input__inner:hover {
  //     border-color: #409EFF
  // }

  // .el-pagination.is-background .el-pager li:not(.disabled):hover {
  //     color: #409EFF
  // }

  // .el-pagination.is-background .el-pager li:not(.disabled).active {
  //     background-color: #409EFF;
  // }
}
</style>
