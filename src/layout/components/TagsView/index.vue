<template>
  <div id="tags-view-container" class="tags-view-container">
    <scroll-pane
      ref="scrollPane"
      class="tags-view-wrapper"
      @scroll="handleScroll"
    >
      <router-link
        v-for="tag in visitedViews"
        ref="tag"
        :key="tag.path"
        :class="isActive(tag) ? 'active' : ''"
        :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"
        tag="span"
        class="tags-view-item"
        :style="activeStyle(tag)"
        @click.middle.native="!isAffix(tag) ? closeSelectedTag(tag) : ''"
        @contextmenu.prevent.native="openMenu(tag, $event)"
      >
        {{ setName(tag) }}
        <!-- <span v-if="!isAffix(tag)" class="el-icon-close" @click.prevent.stop="closeSelectedTag(tag)" /> -->
        <img
          v-if="!isAffix(tag)"
          class="icon-close"
          :src="close"
          alt=""
          @click.prevent.stop="closeSelectedTag(tag)"
        />
      </router-link>
    </scroll-pane>
    <ul
      v-show="visible"
      :style="{ left: left + 'px', top: top + 'px' }"
      class="contextmenu"
    >
      <li @click="refreshSelectedTag(selectedTag)">
        <i class="el-icon-refresh-right"></i> {{ $t("tagsView.refresh") }}
      </li>
      <li v-if="!isAffix(selectedTag)" @click="closeSelectedTag(selectedTag)">
        <i class="el-icon-close"></i> {{ $t("tagsView.closeCurrent") }}
      </li>
      <li v-if="!isOtherView()" @click="closeOthersTags">
        <i class="el-icon-circle-close"></i> {{ $t("tagsView.closeOther") }}
      </li>
      <li v-if="!isFirstView()" @click="closeLeftTags">
        <i class="el-icon-back"></i> {{ $t("tagsView.closeLeft") }}
      </li>
      <li v-if="!isLastView()" @click="closeRightTags">
        <i class="el-icon-right"></i> {{ $t("tagsView.closeRight") }}
      </li>
      <li @click="closeAllTags(selectedTag)">
        <i class="el-icon-circle-close"></i> {{ $t("tagsView.closeAll") }}
      </li>
    </ul>
  </div>
</template>

<script>
import ScrollPane from "./ScrollPane";
import path from "path";
import close from "@/assets/images/close.svg";
import { getFirstPage } from "@/utils/util";
export default {
  components: { ScrollPane },
  data() {
    return {
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
      affixTags: [],
      close,
    };
  },
  computed: {
    visitedViews() {
      return this.$store.state.tagsView.visitedViews;
    },
    routes() {
      return this.$store.state.permission.routes;
    },
    navBackground() {
      return this.$store.state.settings.navBackground;
    },
    navBorder() {
      return this.$store.state.settings.navBorder;
    },
  },
  watch: {
    $route() {
      this.addTags();
      this.moveToCurrentTag();
    },
    visible(value) {
      if (value) {
        document.body.addEventListener("click", this.closeMenu);
      } else {
        document.body.removeEventListener("click", this.closeMenu);
      }
    },
  },
  mounted() {
    // this.beforeUnload()
    this.initTags();
    this.addTags();
  },
  methods: {
    isActive(route) {
      if (route.meta && route.meta.activeMenu) {
        return route.meta.activeMenu == this.$route.meta.activeMenu;
      }
      return route.path === this.$route.path;
    },
    activeStyle(tag) {
      if (!this.isActive(tag)) return {};
      return {
        "background-color": this.navBackground,
        "border-color": this.navBorder,
      };
    },
    isAffix(tag) {
      return tag.meta && tag.meta.affix;
    },
    isFirstView() {
      try {
        return (
          this.visitedViews.length == 1 ||
          this.selectedTag.fullPath === this.visitedViews[0].fullPath
        );

        // return (
        //   this.selectedTag.fullPath === this.visitedViews[1].fullPath ||
        //   this.selectedTag.fullPath === "/index"
        // );
      } catch (err) {
        return false;
      }
    },
    isLastView() {
      try {
        return (
          this.selectedTag.fullPath ===
          this.visitedViews[this.visitedViews.length - 1].fullPath
        );
      } catch (err) {
        return false;
      }
    },
    isOtherView() {
      return this.visitedViews.length == 1;
    },
    filterAffixTags(routes, basePath = "/") {
      let tags = [];
      routes.forEach((route) => {
        if (route.meta && route.meta.affix) {
          const tagPath = path.resolve(basePath, route.path);
          tags.push({
            fullPath: tagPath,
            path: tagPath,
            name: route.name,
            meta: { ...route.meta },
          });
        }
        if (route.children) {
          const tempTags = this.filterAffixTags(route.children, route.path);
          if (tempTags.length >= 1) {
            tags = [...tags, ...tempTags];
          }
        }
      });
      return tags;
    },
    initTags() {
      const affixTags = (this.affixTags = this.filterAffixTags(this.routes));
      for (const tag of affixTags) {
        // Must have tag name
        if (tag.name) {
          this.$store.dispatch("tagsView/addVisitedView", tag);
        }
      }
    },
    addTags() {
      const { name, meta } = this.$route;
      if (name) {
        if (meta.activeMenu) {
          this.$route.meta.title = this.findRouterName();
        }
        this.$store.dispatch("tagsView/addView", this.$route);
      }
      return false;
    },
    findRouterName() {
      let title = "";
      let { name, meta } = this.$route;

      const findRouterTitle = (routes) => {
        for (let route of routes) {
          if (
            route.name === name &&
            route.meta.activeMenu === meta.activeMenu
          ) {
            const currentObj = routes.find(
              (item) =>
                item.meta.activeMenu === meta.activeMenu && !item.hidden,
            );
            if (currentObj) {
              title = this.isZh()
                ? currentObj.meta.title
                : currentObj.meta.titleEn;
              return true;
            }
          }
          if (route.children && route.children.length > 0) {
            if (findRouterTitle(route.children)) {
              return;
            }
          }
        }
        return false;
      };

      this.routes.forEach((item) => {
        if (findRouterTitle(item?.children ?? [])) {
          return;
        }
      });
      return title;
    },
    moveToCurrentTag() {
      const tags = this.$refs.tag;
      this.$nextTick(() => {
        for (const tag of tags) {
          if (tag.to.path === this.$route.path) {
            this.$refs.scrollPane.moveToTarget(tag);
            // when query is different then update
            if (tag.to.fullPath !== this.$route.fullPath) {
              this.$store.dispatch("tagsView/updateVisitedView", this.$route);
            }
            break;
          }
        }
      });
    },
    refreshSelectedTag(view) {
      this.$tab.refreshPage(view);
    },
    closeSelectedTag(view) {
      this.$tab.closePage(view).then(({ visitedViews }) => {
        if (this.isActive(view)) {
          this.toLastView(visitedViews, view);
        }
      });
    },
    closeRightTags() {
      this.$tab.closeRightPage(this.selectedTag).then((visitedViews) => {
        if (!visitedViews.find((i) => i.fullPath === this.$route.fullPath)) {
          this.toLastView(visitedViews);
        }
      });
    },
    closeLeftTags() {
      this.$tab.closeLeftPage(this.selectedTag).then((visitedViews) => {
        if (!visitedViews.find((i) => i.fullPath === this.$route.fullPath)) {
          this.toLastView(visitedViews);
        }
      });
    },
    closeOthersTags() {
      this.$router.push(this.selectedTag).catch(() => {});
      this.$tab.closeOtherPage(this.selectedTag).then(() => {
        this.moveToCurrentTag();
      });
    },
    closeAllTags(view) {
      this.$tab.closeAllPage().then(({ visitedViews }) => {
        if (this.affixTags.some((tag) => tag.path === this.$route.path)) {
          return;
        }
        this.toLastView(visitedViews, view);
      });
    },
    toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0];
      if (latestView) {
        this.$router.push(latestView.fullPath);
      } else {
        // // now the default is to redirect to the home page if there is no tags-view,
        // // you can adjust it according to your needs.
        // if (view.name === "Dashboard") {
        //   // to reload home page
        //   // this.$router.replace({ path: '/redirect' + view.fullPath })
        //   this.$router.replace({ path: "/redirect/index" });
        // } else {
        //   this.$router.push("/");
        // }

        // 默认菜单第一个
        let firstNav = getFirstPage(
          this.$store.state.permission.addRoutes,
          this.$router,
        );
        // 如果最后一个页面和第一个相同则存到viewTag里，否则则push
        if (view.name == firstNav.name) {
          this.$store.dispatch("tagsView/addView", this.$route);
        } else {
          this.$router.push({
            ...firstNav,
          });
        }
      }
    },
    openMenu(tag, e) {
      const menuMinWidth = 105;
      const offsetLeft = this.$el.getBoundingClientRect().left; // container margin left
      const offsetWidth = this.$el.offsetWidth; // container width
      const maxLeft = offsetWidth - menuMinWidth; // left boundary
      const left = e.clientX - offsetLeft + 15; // 15: margin right

      if (left > maxLeft) {
        this.left = maxLeft;
      } else {
        this.left = left;
      }

      this.top = e.clientY;
      this.visible = true;
      this.selectedTag = tag;
    },
    closeMenu() {
      this.visible = false;
    },
    handleScroll() {
      this.closeMenu();
    },
    setName(tag) {
      let title = tag.title;
      if (tag.query && Object.keys(tag.query).length > 0 && tag.query.isEdit) {
        if (tag.query.isEdit == 0) {
          // 新增
          title = this.$t("common.add") + tag.title;
        } else {
          title = this.$t("common.edit") + tag.title;
        }
      }
      return title;
    },
    // beforeUnload() {
    //   // 监听页面刷新
    //   window.addEventListener("beforeunload", () => {
    //     // visitedViews数据结构太复杂无法直接JSON.stringify处理，先转换需要的数据
    //     let tabViews = this.visitedViews.map(item => {
    //       return {
    //         fullPath: item.fullPath,
    //         hash: item.hash,
    //         meta: { ...item.meta },
    //         name: item.name,
    //         params: { ...item.params },
    //         path: item.path,
    //         query: { ...item.query },
    //         title: this.isZh() ? item.title || 'no-name' : this.$i18n.t(`route.${item.title}`)
    //       };
    //     });
    //     sessionStorage.setItem("tabViews", JSON.stringify(tabViews));
    //   });
    // },
  },
};
</script>

<style lang="scss" scoped>
.tags-view-container {
  height: 40px;
  width: 100%;
  background: #fff;
  border-bottom: 1px solid #d8dce5;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);

  .tags-view-wrapper {
    .tags-view-item {
      display: inline-block;
      position: relative;
      cursor: pointer;
      height: 28px;
      line-height: 28px;
      border: 1px solid #d8dce5;
      color: #495060;
      background: #fff;
      // padding: 0 8px;
      padding: 0px 8px 0 12px;
      font-size: 14px;
      margin-left: 5px;
      margin-top: 5px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &:first-of-type {
        margin-left: 15px;
      }

      &:last-of-type {
        margin-right: 15px;
      }

      &.active {
        // background-color: #42b983;
        // color: #fff;
        // border-color: #42b983;
        color: #444444;
        // background-color:  var(--color-primary19);
        background-color: var(--color-primary19) !important;

        border-color: var(--color-primary) !important;
        // &::before {
        //   content: '';
        //   background: #fff;
        //   display: inline-block;
        //   width: 8px;
        //   height: 8px;
        //   border-radius: 50%;
        //   position: relative;
        //   margin-right: 2px;
        // }
      }
    }
  }

  .contextmenu {
    margin: 0;
    background: #fff;
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);

    li {
      margin: 0;
      padding: 7px 16px;
      cursor: pointer;

      &:hover {
        background: #eee;
      }
    }
  }

  .icon-close {
    margin-left: 8px;

    &:hover {
      background-color: #d1d9ee;
      color: #fff;
    }
  }
}
</style>

<style lang="scss">
//reset element css of el-icon-close
.tags-view-wrapper {
  .el-scrollbar__view {
    display: flex;
  }

  .tags-view-item {
    .el-icon-close {
      width: 16px;
      height: 16px;
      // vertical-align: 2px;
      // border-radius: 50%;
      font-size: 18px;
      text-align: center;
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      transform-origin: 100% 50%;
      color: #444444;

      &:before {
        transform: scale(0.6);
        display: inline-block;
        vertical-align: -1px;
      }

      &:hover {
        background-color: #b4bccc;
        color: #fff;
      }
    }
  }
}
</style>
