<template>
  <div
    class="sidebar-footer"
    v-if="version"
    :class="settings.sideTheme"
    :style="{
      color:
        settings.sideTheme == 'theme-custom'
          ? settings.menuColorCustom
          : settings.sideTheme === 'theme-dark' ||
            settings.sideTheme === 'theme-theme'
          ? variables.menuColor
          : variables.menuLightColor,
    }"
  >
    <span v-if="!collapse">
      {{ `${$t("common.version")}:` }}
    </span>
    {{ version }}
  </div>
</template>
<script>
import { mapState, mapGetters } from "vuex";
import variables from "@/assets/styles/variables.scss";

export default {
  name: "sider-footer",
  props: {
    collapse: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    ...mapGetters(["version"]),
    ...mapState(["settings"]),
    variables() {
      return variables;
    },
  },
};
</script>
<style lang="scss" scoped>
.sidebar-footer {
  height: 36px;
  line-height: 36px;
  font-size: 12px;
  text-align: center;
}
</style>
