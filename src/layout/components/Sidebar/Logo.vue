<template>
  <div
    class="sidebar-logo-container"
    :class="{ collapse: collapse }"
    :style="{
      backgroundColor:
        sideTheme == 'theme-custom'
          ? menuBackgroundCustom
          : sideTheme === 'theme-dark'
          ? variables.menuBackground
          : sideTheme === 'theme-theme'
          ? $store.state.settings.theme
          : variables.menuLightBackground,
    }"
  >
    <transition name="sidebarLogoFade">
      <!-- 收缩 -->
      <div v-if="collapse" key="collapse" class="sidebar-logo-link" to="/">
        <img v-if="logo" :src="logo" class="sidebar-logo" />
        <h1
          v-if="!logo && isTitleLogo"
          class="sidebar-title"
          :style="{
            color: titleLogoColor,
            fontStyle: titleLogoFontStyle ? 'italic' : 'normal',
          }"
        >
          {{ $store.state.sys.productName }}
        </h1>
        <img
          v-if="!logo && !isTitleLogo"
          :src="titleImg"
          class="sidebar-logo-name"
          v-imageError="require('@/assets/logo/transparent.svg')"
        />
      </div>
      <!-- 展开 -->
      <div v-else key="expand" class="sidebar-logo-link" to="/">
        <img
          v-if="isTitleLogo"
          :src="logo"
          class="sidebar-logo"
          v-imageError="require('@/assets/logo/transparent.svg')"
        />
        <h1
          v-if="isTitleLogo"
          class="sidebar-title"
          :style="{
            color: titleLogoColor,
            fontStyle: titleLogoFontStyle ? 'italic' : 'normal',
          }"
        >
          {{ $store.state.sys.productName }}
        </h1>
        <img
          v-else
          :src="titleImg"
          class="sidebar-logo-name"
          v-imageError="require('@/assets/logo/transparent.svg')"
        />
      </div>
    </transition>
  </div>
</template>

<script>
import transparentLogo from "@/assets/logo/transparent.svg";

import logoImg from "@/assets/images/sysMiniLogo.svg";
import titleImg from "@/assets/images/sysLogo.svg";
import variables from "@/assets/styles/variables.scss";
import { mapGetters } from "vuex";
export default {
  name: "SidebarLogo",
  props: {
    collapse: {
      type: Boolean,
      required: true,
    },
  },
  computed: {
    ...mapGetters(["sysImg", "sysLogo"]),
    isSysImgEnd() {
      return this.$store.state.sys.isSysImgEnd;
    },
    variables() {
      return variables;
    },
    sideTheme() {
      return this.$store.state.settings.sideTheme;
    },
    menuBackgroundCustom() {
      return this.$store.state.settings.menuBackgroundCustom;
    },
    // logo图标
    logo() {
      return !this.isSysImgEnd
        ? transparentLogo
        : this.sysLogo
        ? this.sysLogo
        : logoImg;
    },
    // 图片title
    titleImg() {
      return !this.isSysImgEnd
        ? transparentLogo
        : this.sysImg
        ? this.sysImg
        : titleImg;
    },
    // 文字title
    isTitleLogo() {
      return this.$store.state.settings.isTitleLogo;
    },
    titleLogo() {
      return this.$store.state.settings.titleLogo;
    },
    titleLogoColor() {
      return this.$store.state.settings.titleLogoColor;
    },
    titleLogoFontStyle() {
      return this.$store.state.settings.titleLogoFontStyle;
    },
  },
  data() {
    return {
      logoImgList: {},
      // logo: logoImg,
      // titleImg
    };
  },
};
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 56px;
  line-height: 56px;
  background: #2b2f3a;
  text-align: center;
  overflow: hidden;
  align-self: stretch;
  border-bottom: 1px solid #ccc;

  // 移动端适配
  @media (max-width: 768px) {
    height: 50px;
    line-height: 50px;
  }

  @media (max-width: 480px) {
    height: 48px;
    line-height: 48px;
  }

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;
    display: flex;
    padding: 2px 5px;
    justify-content: left;
    align-items: center;

    // 移动端适配
    @media (max-width: 768px) {
      padding: 2px 4px;
    }

    @media (max-width: 480px) {
      padding: 1px 3px;
    }
    & .sidebar-logo {
      //   width: 32px;
      max-height: 100%;
      max-width: 100%;
      vertical-align: middle;
      margin-right: 12px;
      padding: 2px;

      // 移动端适配
      @media (max-width: 768px) {
        margin-right: 8px;
        padding: 1px;
      }

      @media (max-width: 480px) {
        margin-right: 6px;
        max-width: 80%;
      }
    }

    & .sidebar-logo-name {
      display: inline-block;
      max-height: 100%;
      max-width: 100%;
      //   width: 100px;
      vertical-align: middle;

      // 移动端适配
      @media (max-width: 768px) {
        max-width: 90%;
      }

      @media (max-width: 480px) {
        max-width: 85%;
      }
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      font-weight: bolder;
      line-height: 50px;
      font-size: 24px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      // 平板端适配 (768px - 1200px)
      @media (max-width: 1200px) and (min-width: 769px) {
        font-size: 20px;
        line-height: 48px;
      }

      // 移动端适配 (≤768px)
      @media (max-width: 768px) {
        font-size: 18px;
        line-height: 46px;
      }

      // 小屏移动端适配 (≤480px)
      @media (max-width: 480px) {
        font-size: 16px;
        line-height: 44px;
        max-width: 120px;
      }
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
</style>
