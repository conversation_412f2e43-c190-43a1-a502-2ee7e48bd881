<template>
  <component :is="type" v-bind="linkProps(to)" @click.native="handleClick">
    <slot />
  </component>
</template>

<script>
import { isExternal } from "@/utils/validate";

export default {
  props: {
    to: {
      type: [String, Object],
      required: true,
    },
  },
  computed: {
    isExternal() {
      return isExternal(this.to);
    },
    type() {
      if (this.isExternal) {
        return "a";
      }
      return "router-link";
    },
  },
  methods: {
    linkProps(to) {
      if (this.isExternal) {
        return {
          href: to,
          target: "_blank",
          rel: "noopener",
        };
      }
      return {
        to: to,
      };
    },
    handleClick() {
      if (!this.isExternal &&
          this.$route.path === this.to &&
          this.$route.meta?.refreshOnSameRoute) {
        this.$router.replace({
          path: '/refreshRedirect',
          query: {
            path: this.to,
            t: Date.now()
          }
        });
      }
    },
  },
};
</script>
