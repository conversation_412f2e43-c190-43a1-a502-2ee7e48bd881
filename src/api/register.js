import request from "@/utils/request";
/**
 * 是否完成初始化
 */
export function isFinishedInit() {
  return new Promise((resolve, reject) => {
    resolve({
      code: "0",
      status: "0",
      data: {
        finish: "Y",
      },
    });
  });

  return request({
    url: "/init/flow/finish",
    method: "get",
  });
}

/**
 * 获取证书中的dn
 */
export function getDn(cert) {
  return request({
    url: "/init/flow/getCertDN",
    method: "get",
    params: {
      cert,
    },
  });
}
