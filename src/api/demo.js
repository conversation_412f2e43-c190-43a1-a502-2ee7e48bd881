import request from "@/utils/request";
import config from "./configs";
import { transFormData } from "@/utils/util";

export function getDemoList(data) {
  return request({
    url: `${config.PLATFORM}/demo/test/list`,
    headers: {
      repeatSubmit: false,
    },
    method: "post",
    data,
  });
}
// 不分页
export function getDemoList2(data) {
  return request({
    url: `${config.PLATFORM}/demo/test/list2`,
    headers: {
      repeatSubmit: false,
    },
    method: "post",
    data,
  });
}

export function getMockTreeList(data) {
  return request({
    url: "/mock/treelist",
    method: "post",
    data,
  });
}

export function getMockChartData(data) {
  return request({
    url: "/mock/chart",
    headers: {
      repeatSubmit: false,
    },
    method: "post",
    data,
  });
}

export function getMockSearchData(data) {
  return request({
    url: "/mock/search",
    headers: {
      repeatSubmit: false,
    },
    method: "post",
    data,
  });
}

export function getDemoDetail(id) {
  return request({
    url: `${config.PLATFORM}/demo/test/detail`,
    method: "get",
    params: { id },
  });
}

export function getDemoDetail2(id) {
  return request({
    url: `${config.PLATFORM}/demo/test/link`,
    method: "get",
    params: { id },
  });
}

// 上传接口
export function uploadFile(data) {
  return request({
    url: `${config.PLATFORM}/demo/test/upload`,
    method: "post",
    data: transFormData(data),
  });
}
