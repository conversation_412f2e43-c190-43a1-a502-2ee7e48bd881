import request from "@/utils/request";

// 角色列表
export function getRoleList(data) {
  return request({
    url: "/sec/role/page",
    method: "post",
    data,
  });
}

// 新增角色
export function saveRole(data) {
  return request({
    url: "/sec/role/add",
    method: "post",
    data,
  });
}
// 编辑角色
export function editRole(data) {
  return request({
    url: "/sec/role/edit",
    method: "post",
    data,
  });
}
// 查询角色详情
export function getRoleDetail(id) {
  return request({
    url: "/sec/role/findById",
    method: "get",
    params: {
      id,
    },
  });
}
// 删除角色
export function deleteRole(ids) {
  return request({
    url: "/sec/role/delete",
    method: "get",
    params: {
      ids,
    },
  });
}

// 新增角色下的菜单权限
export function saveRoleMenu(data) {
  return request({
    url: "/sec/role/menu/auth",
    method: "post",
    data,
  });
}
// 获取角色
export function getAllRole(params) {
  return request({
    url: "/sec/role/list",
    method: "get",
    params,
  });
}
// 获取角色 租户/用户
export function getAllTenantRole(params) {
  return request({
    url: "/sec/role/listByRoleType",
    method: "get",
    params,
  });
}
