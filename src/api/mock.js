import request from "@/utils/request";

export function getMockList(data) {
  return request({
    url: "/mock/list",
    method: "post",
    data,
  });
}
export function getMockTreeList(data) {
  return request({
    url: "/mock/treelist",
    method: "post",
    data,
  });
}
export function addMock(data) {
  return request({
    url: "/mock/add",
    method: "post",
    data,
  });
}
export function editMock(data) {
  return request({
    url: "/mock/edit",
    method: "post",
    data,
  });
}
export function delMock(data) {
  return request({
    url: "/mock/del",
    method: "post",
    data,
  });
}

export function addForm(data) {
  return request({
    url: "/mock/add/form",
    method: "post",
    data,
  });
}

export function getTransferData(data) {
  return request({
    url: "/mock/transfer/data",
    method: "post",
    data,
  });
}
