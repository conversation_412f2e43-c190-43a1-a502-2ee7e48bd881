import request from "@/utils/request";
import { menu } from "@Project/config/menu";

// 获取路由
export const getRouters = () => {
  return new Promise((resolve, reject) => {
    resolve({
      requestId: null,
      code: "0",
      status: "0",
      message: "操作成功！",
      timestamp: null,
      costMillis: 0,
      data: menu,
      success: true,
    });
  });
  return request({
    url: "/menu/queryCatalogue",
    method: "get",
  });
};
// 获取所有菜单
export function getAllMenuList(roleId) {
  return request({
    url: "/menu/queryMenus",
    method: "get",
    params: {
      roleId,
    },
  });
}

// 获取该角色选中的菜单
export function getMenuFormRule(id) {
  return request({
    url: "/menu/queryLastMenuNode",
    method: "get",
    params: {
      roleId: id,
    },
  });
}
// 获取首页菜单url
export function getIndexMenu() {
  return request({
    url: "/menu/queryIndexPage",
    method: "get",
  });
}

// 获取用户拥有的按钮权限
export function getOperAuth() {
  return new Promise((resolve, reject) => {
    resolve({
      requestId: null,
      code: "0",
      status: "0",
      message: "操作成功！",
      timestamp: null,
      costMillis: 0,
      data: [{ menuMark: "none:none:none" }],
      success: true,
    });
  });
  return request({
    url: "/menu/queryCurrentUserMenus",
    params: {
      menuType: 1,
    },
  });
}
