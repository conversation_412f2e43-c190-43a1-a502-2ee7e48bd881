import request from '@/utils/request'


// 获取门户欢迎信息
export function getWelcomeData() {
  return request({
    url: '/portal/welcome',
    method: 'get'
  })
}

// 获取推荐应用列表
export function getRecommendedApps() {
  return request({
    url: '/portal/recommended-apps',
    method: 'get'
  })
}

// 发送聊天消息
export function sendChatMessage(data) {
  return request({
    url: '/portal/chat/send',
    method: 'post',
    data
  })
}

// 上传文件
export function uploadChatFile(data, authToken) {
  // 获取当前页面URL的基础部分
  const currentUrl = new URL(window.location.href);
  const baseUrl = currentUrl.origin; // 自动包含协议、域名和端口
  
  return request({
    url: `${baseUrl}/api/files/upload`, // 使用动态基础URL
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
      'Authorization': `Bearer ${authToken}`
    }
  })
}

