<template>
  <span class="text-tag-com">
    <span
      class="el-icon-loading"
      v-show="checkLoading"
      style="font-size: 14px"
    ></span>
    <slot name="title"></slot>
    <slot name="content"></slot>
  </span>
</template>
<script>
export default {
  name: "textTagCom",
  props: {
    options: {
      type: Array,
      default: null,
    },
    labelName: {
      type: String,
      default: "label",
    },
    valueName: {
      type: String,
      default: "value",
    },
    value: [Number, String, Array],
    styles: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  computed: {
    checkLoading() {
      let item = this.options.find((key) => key[this.valueName] == this.value);
      if (item?.loading == true) {
        return true;
      }
      if (this.styles[item?.[this?.valueName]]?.loading == true) {
        return true;
      }
      return false;
    },
  },
};
</script>
<style lang="scss" scoped>
.text-tag-com {
  display: inline-block;
  border: 1px solid;
  padding: 0px 8px;
  line-height: 18px;
  font-size: 12px;
  border-radius: 20px;
}
</style>
