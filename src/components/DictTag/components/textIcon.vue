<template>
  <span class="text-icon-com">
    <i :class="getIconClass"></i>
    <slot name="title"></slot>
    <slot name="content"></slot>
  </span>
</template>
<script>
export default {
  name: "textIconCom",
  props: {
    options: {
      type: Array,
      default: null,
    },
    labelName: {
      type: String,
      default: "label",
    },
    valueName: {
      type: String,
      default: "value",
    },
    value: [Number, String, Array],
    styles: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  computed: {
    getIconClass() {
      let item = this.options.find((key) => key[this.valueName] == this.value);
      if (item.listClass) {
        return `${item.listClass} ${item.icon}`;
      }
      return `${this.styles[this.value].listClass} ${
        this.styles[this.value].icon
      }`;
    },
  },
};
</script>
<style lang="scss" scoped>
.text-icon-com {
  i {
    margin-right: 4px;
  }
}
</style>
