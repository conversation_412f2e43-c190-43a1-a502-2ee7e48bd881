<template>
  <div
    class="dict-tag-com-container"
    v-if="value !== null && value !== undefined"
  >
    <component
      :class="`${comType}-class`"
      :is="`${comType}Com`"
      :value="value"
      :options="options"
      :labelName="labelName"
      :valueName="valueName"
      :styles="styles"
    >
      <template #content v-if="content">
        <span :class="titleClass"> {{ content }}</span>
      </template>
      <template #title v-else>
        <span :class="titleClass">
          {{ selectDictText(options, value, valueName, labelName) }}</span
        >
      </template>
    </component>
  </div>
</template>
<script>
import textCom from "./components/text.vue";
import textIconCom from "./components/textIcon.vue";
import textSpotCom from "./components/textSpot.vue";
import tagCom from "./components/tag.vue";
import textTagCom from "./components/textTag";
import { selectDictText } from "@/utils/util";
export default {
  name: "TableStatus",
  components: {
    textCom,
    textIconCom,
    textSpotCom,
    tagCom,
    textTagCom,
  },
  props: {
    options: {
      type: Array,
      default: () => [],
    },
    value: [Number, String, Array],
    labelName: {
      type: String,
      default: "label",
    },
    valueName: {
      type: String,
      default: "value",
    },
    styles: {
      type: Object,
      default: () => {
        // tag: {
        //   type: 'success、warning、'''
        // }
        // text: {
        //   titleClass: 'success-color、warning-color、default-color'
        // }
        // text-icon {
        //   titleClass: 'success-color、warning-color、default-color',
        //   icon: "",
        //   iconClass: ""
        // }
        // text-spot {
        //    titleClass: 'success-color、warning-color、default-color',
        //    iconClass: "success-bg-color、warning-bg-color、default-bg-color"
        // }
      },
    },
    comType: {
      // 组件类型
      type: String,
      default: "tag", // tag、text、text-icon、text-spot、
    },
    content: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      selectDictText,
    };
  },
  computed: {
    titleClass() {
      let item = this.options.find((key) => key[this.valueName] == this.value);
      if (!item) {
        return "";
      }
      if (item.titleClass) {
        return item.titleClass;
      }
      if (this.styles && this.styles[this.value]) {
        return this.styles[this.value].titleClass;
      }
      return "";
    },
  },
};
</script>
<style lang="scss">
.dict-tag-com-container {
  display: inline-block;
  .success {
    color: var(--color-success);
  }

  .warning {
    color: var(--color-warning);
  }

  .primary {
    color: var(--color-primary);
  }
  .danger {
    color: var(--color-danger);
  }

  .info {
    color: var(--color-info);
  }

  .text-spot-class {
    .success {
      background-color: var(--color-success);
    }

    .warning {
      background-color: var(--color-warning);
    }

    .primary {
      background-color: var(--color-primary);
    }
    .danger {
      background-color: var(--color-danger);
    }

    .info {
      background-color: var(--color-info);
    }
  }

  // tag  文本
  .text-tag-class:has(.success) {
    border-color: var(--color-success);
  }
  .text-tag-class:has(.warning) {
    border-color: var(--color-warning);
  }
  .text-tag-class:has(.primary) {
    border-color: var(--color-primary);
  }
  .text-tag-class:has(.danger) {
    border-color: var(--color-danger);
  }
  .text-tag-class:has(.info) {
    border-color: var(--color-info);
  }
}
</style>
