<template>
  <div class="common-no-data">
    <div class="common-no-data-icon">
      <svg-icon :icon-class="icon" />
    </div>
    {{ title || $t("common.noData") }}
  </div>
</template>
<script>
export default {
  name: "SwNoData",
  props: {
    title: {
      type: String,
      default: "",
    },
    icon: {
      type: String,
      default: "icon-empty",
    },
  },
  data() {
    return {};
  },
};
</script>
<style lang="scss" scoped>
.common-no-data {
  background: #ffffff;
  padding: 50px;
  text-align: center;
  color: #909399;
  line-height: 1.5;
  .common-no-data-icon {
    svg {
      max-width: 120px;
      max-height: 120px;
      width: auto;
      height: auto;
    }
  }
}
</style>
