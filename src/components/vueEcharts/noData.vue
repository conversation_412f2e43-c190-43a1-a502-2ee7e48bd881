<template>
  <div class="no-data full-height full-width">
    <div class="common-no-data-icon">
      <svg-icon :icon-class="icon" />
    </div>
    <slot>
      <span class="no-data-default">{{ text }}</span>
    </slot>
  </div>
</template>

<script>
import i18n from "@/i18n";
export default {
  name: "noData",
  props: {
    text: {
      type: String,
      default: i18n.t("common.noData"),
    },
    icon: {
      type: String,
      default: "icon-empty",
    },
  },
  data() {
    return {};
  },
};
</script>
<style lang="scss" scoped>
.no-data {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  flex-direction: column;
  .common-no-data-icon {
    height: 40%;
    svg {
      height: 100%;
      width: 100%;
      max-width: 120px;
      max-height: 120px;
    }
  }
  .no-data-default {
    font-size: 22px;
    color: #dbdbdb;
  }
}
</style>
