<template>
  <div style="position: relative">
    <div
      :class="{
        disabled: disabled,
        'is-error-ip': !!this.errorMsg,
        'is-focus-ip': this.isFocus,
      }"
      class="ip_box"
      id="ip_box"
    >
      <template v-for="index in 4">
        <el-input
          class="ip_box_input"
          :ref="`ip${index}`"
          :disabled="disabled"
          v-model="form[`ip${index}`]"
          maxlength="3"
          @input="form[`ip${index}`] = form[`ip${index}`].replace(/[^\d]/g, '')"
          @keyup.native="keyupEvent(1, $event)"
          @blur="blurFun(index)"
          @focus="isFocus = true"
        />
        <div class="ip_dot" v-if="index !== 4" />
      </template>
    </div>
    <!-- 模拟错误提示 -->
    <transition name="el-zoom-in-top">
      <div class="is-error-ip__error" v-if="errorMsg">{{ errorMsg }}</div>
    </transition>
  </div>
</template>

<script>
import { validIp } from "@/utils/validate";
export default {
  name: "IpInput",
  model: {
    prop: "ipAddress",
    event: "change",
  },
  props: {
    ipAddress: {
      type: String,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    required: {
      // 是否必填
      type: Boolean,
      default: false,
    },
    isValid: {
      // 是否校验
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      form: {
        ip1: "",
        ip2: "",
        ip3: "",
        ip4: "",
      },
      isFocus: false, // 4个输入框是否存在焦点
      errorMsg: "",
    };
  },
  watch: {
    ipAddress: {
      handler(val) {
        if (val !== null && val !== "") {
          const ipList = val.split(".");
          this.form.ip1 = ipList[0];
          this.form.ip2 = ipList[1];
          this.form.ip3 = ipList[2];
          this.form.ip4 = ipList[3];
        }
      },
      deep: true,
      immediate: true,
    },
    "form.ip1": {
      handler(val) {
        this.$nextTick(() => {
          if (this.form.ip1.length === 3) {
            this.$refs.ip2[0].focus();
          }
        });
      },
      deep: true,
      immediate: true,
    },
    "form.ip2": {
      handler(val) {
        this.$nextTick(() => {
          if (this.form.ip2.length === 3) {
            this.$refs.ip3[0].focus();
          }
        });
      },
      deep: true,
      immediate: true,
    },
    "form.ip3": {
      handler(val) {
        this.$nextTick(() => {
          if (this.form.ip3.length === 3) {
            this.$refs.ip4[0].focus();
          }
        });
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    document.addEventListener("click", this.getFocusStatus);
  },
  beforeDestroy() {
    document.removeEventListener("click", this.getFocusStatus);
  },
  methods: {
    // 判断4个输入框是否全部失去焦点
    getFocusStatus(event) {
      if (this.isFocus) {
        var targetElement = event.target;
        var element = document.getElementById("ip_box");
        if (targetElement !== element && !element.contains(targetElement)) {
          console.log("失焦");
          this.isFocus = false;
          // 组合IP，传给父组件
          const ipVal =
            this.form.ip1 +
            "." +
            this.form.ip2 +
            "." +
            this.form.ip3 +
            "." +
            this.form.ip4;
          this.$emit("change", ipVal);
          // 判断是否满足校验
          this.checkIp(ipVal);
        }
      }
    },
    checkIp(ip) {
      // 是否需要校验
      if (this.isValid) {
        // 为空时
        if (!ip || ip === "...") {
          // 是否必填
          if (this.required) {
            this.errorMsg = this.$t("placeholder.ip");
            return;
          }
        } else {
          if (!validIp(ip)) {
            this.errorMsg = this.$t("validate.ip");
            return;
          }
        }
        this.errorMsg = "";
      } else {
        this.errorMsg = "";
      }
    },
    // 去0
    blurFun(index) {
      if (
        this.form[`ip${index}`] === "0" ||
        this.form[`ip${index}`] === "00" ||
        this.form[`ip${index}`] === "000"
      ) {
        this.form[`ip${index}`] = "0";
      } else {
        this.form[`ip${index}`] = this.form[`ip${index}`].replace(
          /\b(0+)/g,
          "",
        );
      }
    },
    // 按下左键和右键焦点左右移动
    keyupEvent(index, e) {
      this.$nextTick(() => {
        // 按下'↑'键焦点左移
        if (e.keyCode === 38) {
          if (index === 2) {
            this.$refs.ip1[0].focus();
          } else if (index === 3) {
            this.$refs.ip2[0].focus();
          } else if (index === 4) {
            this.$refs.ip3[0].focus();
          }
        } else if (e.keyCode === 40) {
          // 按下'↓'键焦点右移
          if (index === 1) {
            this.$refs.ip2[0].focus();
          } else if (index === 2) {
            this.$refs.ip3[0].focus();
          } else if (index === 3) {
            this.$refs.ip4[0].focus();
          }
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.ip_box {
  width: 100%;
  min-width: 165px;
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

  ::v-deep .el-input__inner {
    border: 0 !important;
    padding: 0;
    text-align: center;
  }
  ::v-deep .el-input {
    min-width: 40px;
    width: calc(25% - 2px);
  }
  &.is-focus-ip {
    border-color: var(--color-primary);
  }
  &.disabled {
    background-color: #f5f7fa;
    color: #c0c4cc;
    cursor: not-allowed;
  }
  &.is-error-ip {
    border-color: #ea4040;
  }
}
.is-error-ip__error {
  position: absolute;
  bottom: -26px;
  left: 0;
  color: #ea4040;
  width: 100%;
  font-size: 12px;
}
.ip_dot {
  margin-top: 7px;
  display: inline-block;
  width: 2px;
  height: 2px;
  border-radius: 50%;
  background-color: #606266;
}
.disabled {
  .ip_dot {
    background-color: #c0c4cc;
  }
}
</style>
