<template>
  <el-select
    v-model="childSelectedValue"
    :filterable="remote"
    :multiple="multiple"
    :loading="loading"
    :remote="remote"
    :size="size"
    :remote-method="remoteMethod"
    :clearable="clearable"
    @change="handleChange"
    @clear="handleClear"
    @focus="handleFocus"
    :placeholder="placeholder"
  >
    <div style="padding: 5px 20px 5px 10px">
      <el-pagination
        layout="prev,pager,next,jumper"
        @current-change="changeNumber"
        :hide-on-single-page="true"
        :page-size="selectPage.pageSize"
        :current-page="selectPage.currentPage"
        :pager-count="selectPage.pagerCount"
        :total="selectPage.total"
      >
      </el-pagination>
    </div>
    <div>
      <el-option
        v-for="item in optionSource"
        :key="item[valueKey]"
        :label="item[labelKey]"
        :value="item[valueKey]"
      >
      </el-option>
    </div>
  </el-select>
</template>
<script>
export default {
  name: "PaginationSelect",
  props: {
    //此参数只是为了父组件实现 v-model指令接受参数用，子组件中无实际意义
    // 在子组件中通过监听childSelectedValue值，来触发 input 事件，实现子父组件数据绑定
    value: {
      type: String,
      default: "",
    },
    valueKey: {
      //传入的option数组中，要作为最终选择项的键值名称
      default: "value",
      type: String,
    },
    labelKey: {
      //传入的option数组中，要作为显示项的键值名称
      default: "label",
      type: String,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    remote: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: "medium",
    },
    loading: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: "",
    },
    optionSource: {
      //下拉框组件数据源
      type: Array,
      required: true,
    },
    selectPage: {
      //分页配置项
      type: Object,
      default: function () {
        return {
          pageSize: 20, //每页显示条数
          currentPage: 1, //当前页
          pagerCount: 5, //按钮数，超过时会折叠
          total: 10, //总条数
        };
      },
    },
  },
  data() {
    return {
      childSelectedValue: this.value,
    };
  },
  watch: {
    //监听子组件中选择的值得变化，每当选择一个项后，触发input事件，
    // 将子组件中选择的值通过input事件传递给父组件，实现父组件中v-model绑定的值的双向绑定
    childSelectedValue(val) {
      this.$emit("input", val);
    },
    value(val) {
      if (val != null && val.length < 1) {
        this.childSelectedValue = "";
      }
    },
  },
  mounted() {},
  methods: {
    //子组件分页器，页码选择事件，父组件中监听子组件的 pageNationChange 事件，获取当前页码
    changeNumber(val) {
      //此处的val是页码
      this.$emit("pageNationChange", val);
    },
    // 远程调用方法，在父组件中实现远程方法
    remoteMethod(val) {
      if (val != null && val.length > 0) {
        //只有输入的字符串长度大于1时，触发
        this.$emit("remote-method", val);
      } else {
        this.childSelectedValue = " ";
      }
    },
    //使组件支持change事件
    handleChange(val) {
      this.$emit("change", val);
    },
    //使组件支持clear事件
    handleClear(val) {
      this.$emit("clear", val);
    },
    //解决远程搜索无结果时，显示清除按钮问题
    handleFocus() {
      if (this.childSelectedValue.length < 1) {
        this.childSelectedValue = "";
      }
    },
  },
};
</script>
<style scoped></style>
