<template>
  <div class="page-header full-width">
    <div class="page-title full-width">
      <div style="flex: 1">
        <b>{{ title }}</b>
        <el-popover
          placement="right"
          v-if="$slots.titleWarning"
          width="400"
          trigger="hover"
        >
          <div>
            <slot name="titleWarning" />
          </div>
          <span slot="reference" class="iconfont icon-help-line"></span>
        </el-popover>
      </div>
      <div class="page-title-right">
        <el-button
          type="text"
          size="mini"
          v-show="$slots.pageFlow"
          @click="showFlow = !showFlow"
          >{{ $t("common.pageFlowTitle") }}</el-button
        >
        <!-- <el-button type="text" size="mini">使用指南</el-button> -->
      </div>
    </div>
    <slot name="default" />
    <div class="flow-box" v-if="$slots.pageFlow && showFlow">
      <div class="flow-title">
        <span>{{ $t("common.pageFlowTitle") }}</span>
        <span
          class="pull-right"
          style="cursor: pointer"
          @click="showFlow = !showFlow"
        >
          <i class="el-icon-close"></i>
        </span>
      </div>
      <slot name="pageFlow" />
      <!-- <el-alert type="warning"> 这是一个页面内容提示 </el-alert> -->
    </div>
  </div>
</template>
<script>
export default {
  name: "pageHeader",
  props: {
    title: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      showFlow: true,
    };
  },
};
</script>
<style lang="scss" scoped>
.page-header {
  .page-title {
    display: flex;
    justify-content: space-between;
    .iconfont {
      margin-left: 5px;
    }
  }
  .flow-box {
    background: #fff;
    padding: 10px;
    border-radius: 4px;
    margin: 10px auto;
    .flow-title {
      margin-bottom: 5px;
    }
    .flow-content {
      display: flex;
      .flow-item {
        margin-right: 24px;
        min-width: 200px;
        position: relative;
        &::after {
          content: "→";
          position: absolute;
          right: -22px;
          font-size: 18px;
          top: 0px;
        }
        &:last-child {
          margin-right: 0px;
          &::after {
            content: "";
          }
        }
        .flow-item-title {
          background: #f5f5f5;
          border: 1px solid #d0d2d4;
          border-radius: 4px;
          padding: 0px 10px;
          margin-bottom: 10px;
          text-align: center;
        }
        .flow-item-desc {
          border: 1px solid #d0d2d4;
          border-radius: 4px;
          padding: 10px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
