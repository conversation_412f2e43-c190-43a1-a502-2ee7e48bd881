<template>
  <el-container
    class="search-table search-table-content"
    :class="{ 'search-table_inner': inner }"
    direction="vertical"
  >
    <div class="comm-page-title" v-if="$slots.pageTitle">
      <slot name="pageTitle" />
      <!-- <span class="title-name">{{ searchTitle }}</span> -->
    </div>
    <el-header
      :height="headerHeight"
      class="search-table__header"
      v-if="$slots.search || $slots.row"
    >
      <search-form
        :model="searchForm"
        ref="form"
        :label-width="labelWidth"
        :expand="open"
        :showExpand="showExpand"
        :showResetBtn="showResetBtn"
        :rules="rules"
        @query="$emit('query')"
        @reset="$emit('reset')"
        @expandChange="calcHeader"
      >
        <template>
          <slot name="row"></slot>
          <slot name="search"></slot>
        </template>
      </search-form>
    </el-header>
    <div :class="isRight || isLeft ? 'content-flex' : 'full-width'">
      <div class="content-left" v-if="isLeft" :style="`width:${leftWidth};`">
        <slot name="leftContent"></slot>
      </div>
      <div
        class="content-box"
        :class="isRight || isLeft ? 'content-sub-flex' : ''"
        :style="isRight || isLeft ? `width:calc(100% - ${leftWidth})` : ''"
      >
        <div class="table-content">
          <div class="search-table-title" v-if="tableTitle">
            <span class="title-name">{{ tableTitle }}</span>
          </div>
          <el-main class="search-table__body" v-loading="tableLoading">
            <el-row
              ref="actions"
              v-if="$slots.actions || operateBtns.length > 0"
              class="search-table__action"
            >
              <el-col :span="24">
                <slot name="actionSearch"></slot>
                <el-checkbox
                  v-if="isSelectedAll"
                  v-model="allChecked"
                  :total="100"
                  style="margin-left: 15px; margin-right: 15px"
                  @change="allChange"
                >
                  {{ $t("common.selectAll") }}
                </el-checkbox>
                <slot name="actions"></slot>
                <action-btns :btns="operateBtns" />
              </el-col>
            </el-row>
            <el-table
              id="tableId"
              ref="tableRef"
              v-bind="$attrs"
              v-on="$listeners"
              :height="tableHeight"
              :data="tableData"
              :empty-text="emptyText"
              :stripe="stripe"
              border
              size="small"
              highlight-current-row
              :row-class-name="rowClassName"
              tooltip-effect="light"
              :header-cell-style="{ fontWeight: 'bold' }"
              @selection-change="
                (selection) => {
                  this.$emit('selectionChange', selection);
                }
              "
              :default-sort="defaultSort"
              @sort-change="handleSortChange"
              @select="
                (selection, row) => {
                  this.$emit('select', { selection, row });
                }
              "
              @select-all="
                (selection) => {
                  this.$emit('selectAll', selection);
                }
              "
              :summary-method="summaryMethod"
              :show-summary="showSummary"
              :tree-props="treeProps"
              v-if="refreshTable"
              :default-expand-all="isExpandAll"
              :row-key="rowKey"
              :lazy="lazy"
              :load="load"
              :cell-style="cellStyle"
              :row-style="rowStyle"
              @expand-change="
                (row, expandedRows) =>
                  this.$emit('expandChange', { row, expandedRows })
              "
              @row-click="
                (row, column, event) => {
                  this.$emit('onRowClick', { row, column, event });
                }
              "
            >
              <el-table-column
                v-if="isSelected"
                type="selection"
                :selectable="selectable"
              >
              </el-table-column>
              <slot v-if="columns.length == 0"></slot>
              <template v-else>
                <el-table-column
                  v-for="(item, index) in columns"
                  :key="index"
                  :label="item.label"
                  :prop="item.prop"
                  v-bind="item || {}"
                  :min-width="item.minWidth"
                  :show-overflow-tooltip="item.tooltip === false ? false : true"
                >
                  <template v-if="item.render" #default="{ row, $index }">
                    <slot :name="item.render" :data="row" :index="$index" />
                  </template>
                </el-table-column>
              </template>
              <div slot="empty" class="empty-container">
                <sw-nodata :title="emptyText" v-if="!$slots.empty" />
                <slot name="empty"> </slot>
              </div>
            </el-table>
          </el-main>
        </div>
        <el-footer class="search-table__footer" height="42px" v-if="isFooter">
          <el-pagination
            :current-page="tablePage.pageNum"
            :page-sizes="[20, 50, 100]"
            :page-size="tablePage.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tablePage.total"
            @size-change="
              (val) => {
                this.$emit('pagination', { pageSize: val, pageNum: 1 });
              }
            "
            @current-change="
              (val) => this.$emit('pagination', { pageNum: val })
            "
          ></el-pagination>
        </el-footer>
      </div>
      <div class="content-right" v-if="isRight" :style="`width:${rightWidth}`">
        <slot name="rightContent"></slot>
      </div>
    </div>
  </el-container>
</template>

<script>
import ResizeMixin from "./mixins/resize";
import SearchForm from "../searchForm/index";
import tableSelection from "./mixins/table.selection.mixin";
import _ from "lodash";

export default {
  name: "SearchTableContent",
  mixins: [ResizeMixin, tableSelection],
  components: { SearchForm },
  props: {
    emptyText: {
      type: String,
      default: "",
    },
    labelWidth: {
      type: String,
      default: "70px",
    },
    expand: Boolean,
    stripe: {
      type: Boolean,
      default: false,
    },
    defaultSort: {
      type: Object,
      default() {
        return {};
      },
    },
    // 嵌入到其他组件中，padding为0,外部自行设置。
    inner: {
      type: Boolean,
      default: false,
    },
    rowClassName: {
      type: Function,
    },
    cellStyle: {
      type: Function,
    },
    rowStyle: {
      type: Function,
    },
    showSummary: {
      type: Boolean,
    },
    summaryMethod: {
      type: Function,
    },
    load: {
      type: Function,
    },
    lazy: {
      type: Boolean,
      default: false,
    },
    treeProps: {
      type: Object,
      default() {
        return {};
      },
    },
    rowKey: {
      type: String,
      default() {
        return "";
      },
    },
    showResetBtn: {
      type: Boolean,
      default: true,
    },
    rules: {
      type: Object,
      default: () => {},
    },
    operateBtns: {
      type: Array,
      default: () => {
        return [];
      },
    },
    searchTitle: {
      type: String,
      default: "",
    },
    tableTitle: {
      type: String,
      default: "",
    },
    isSelected: {
      type: Boolean,
      default: false,
    },
    isRight: {
      type: Boolean,
      default: false,
    },
    isLeft: {
      type: Boolean,
      default: false,
    },
    isSelectedAll: {
      type: Boolean,
      default: false,
    },
    isControlSelect: {
      type: Boolean,
      default: false,
    },
    selectable: {
      type: Function,
    },
    rightWidth: {
      type: String,
      default: "416px",
    },

    leftWidth: {
      type: String,
      default: "416px",
    },
    isFooter: {
      type: Boolean,
      default: true,
    },
    columns: {
      type: Array,
      default: () => {
        return [];
      },
    },
    searchForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
    tableLoading: {
      type: Boolean,
      default: false,
    },
    tableData: {
      type: Array,
      default: () => {
        return [];
      },
    },
    tablePage: {
      type: Object,
      default: () => {
        return {
          total: 1,
          pageSize: 20,
          pageNum: 1,
        };
      },
    },
    isDig: {
      type: Boolean,
      default: false,
    },
    heightAuto: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      open: false,
      headerHeight: "64px",
      tableHeight: this.heightAuto ? null : this.isDig ? "350px" : "200px",
      showExpand: false,
      isExpandAll: false,
      refreshTable: true,
      rightHeight: "200px",
    };
  },
  watch: {
    headerHeight() {
      this.resetTableHeight();
    },
  },
  mounted() {
    if (this.expand) {
      this.calcHeader(this.expand);
    }
    this.$nextTick(() => {
      this.resetHeight();
    });
  },
  methods: {
    resetHeight() {
      this.calcHeader(this.open);
      this.resetTableHeight();
      this.showExpand =
        this.$slots.search || this.$slots.row
          ? this.$refs.form.getRowHeight() > 64
          : false;
    },
    getHeight(className) {
      return (
        this.$el.querySelector(className)?.getBoundingClientRect()?.height || 0
      );
    },
    resetTableHeight() {
      if (this.heightAuto) {
        this.tableHeight = null;
        return;
      }
      this.$nextTick(() => {
        let height = this.$el.clientHeight - parseInt(this.headerHeight, 0);
        height = height - 18;
        // let tableTopHeight = this.getHeight(".search-table-title");
        // height -= tableTopHeight;
        height = height - (this.searchTitle.length == "" ? 0 : 36);
        height = height - (this.isFooter ? 42 : 0) - 16;
        height = height - this.calcActionsHeader();
        height = height - (this.tableTitle.length == "" ? 0 : 36);
        // if (this.inner == true) {
        //   height = height + 40;
        // }
        if (!this.$slots.search && !this.$slots.row) {
          height = height + 16;
        }
        height -= 30;

        let rightHeight =
          this.$el.clientHeight - parseInt(this.headerHeight, 0) - 16;
        this.rightHeight = `${rightHeight}px`;
        this.tableHeight = `${height}px`;
      });
    },
    calcHeader(flag) {
      this.open = flag;
      if (this.$slots.search || this.$slots.row) {
        if (flag) {
          this.headerHeight = `${this.$refs.form.$el.clientHeight + 16}px`;
        } else {
          this.headerHeight = `64px`;
        }
      } else {
        this.headerHeight = "0";
      }
    },
    calcActionsHeader() {
      const el = this.$refs.actions ? this.$refs.actions.$el : null;
      if (el) {
        return el.clientHeight;
      } else {
        return -16;
      }
    },
    handleSortChange(event) {
      const { prop, order } = event;
      this.$emit("sortChange", {
        prop,
        order: order
          ? String.prototype.replace.call(order, "ending", "")
          : null,
      });
    },
    resetField() {
      if (this.$refs.form) {
        console.log(this.$refs.form);
        this.$refs.form.resetField();
      }
    },
    clearSelection() {
      this.$refs.tableRef.clearSelection();
    },
    getTableRef() {
      return this.$refs.tableRef;
    },

    //全选
    allChange() {
      this.debounceMethod();
    },
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },

    //防抖
    debounceMethod: _.debounce(
      function () {
        console.log("allChecked", this.allChecked);
      },
      300,
      {
        leading: false, //指定在延迟开始前调用
        trailing: true, //指定在延迟结束后调用
      },
    ),
  },
};
</script>
<style lang="scss" scoped>
.search-table-title {
  margin-bottom: 8px;
}
</style>
<style lang="scss">
.search-table-content {
  .search-table__header {
    background: #ffffff;
    box-shadow: 0px 0px 10px 4px var(--color-primary19);
    border-radius: 4px;
    margin-bottom: 16px;
    overflow: hidden;
    padding: 16px 16px 0;
  }
  .search-form {
    .el-form-item {
      margin-bottom: 16px;
      padding: 0 8px 0 0;
      .el-form-item__label {
        font-size: 14px;
      }
      .el-form-item__content {
        .el-select {
          width: 200px;
        }
        .el-input {
          width: 200px;
        }
      }
    }
  }
  .content-flex {
    display: flex;
    width: 100%;
  }
  .content-right,
  .content-left,
  .content-sub-flex {
    // width: 416px;
    background: #ffffff;
    box-shadow: 0px 0px 10px 4px var(--color-primary19);
    border-radius: 4px;
    // height: 100%;
    overflow-y: auto;
  }
  .content-right {
    margin-left: 16px;
    // width: 416px;
  }
  .content-left {
    margin-right: 16px;
    // width: 416px;
  }
  .content-box {
    box-shadow: 0px 0px 10px 4px var(--color-primary19);
  }
  .content-sub-flex {
    width: 0px;
    flex: 1;
  }
  .table-content {
    background: #ffffff;
    border-radius: 4px;
    padding: 16px;
  }
  .search-table__body {
    padding: 0;
  }
  .search-table__action {
    margin-bottom: 16px;
    .el-button + .el-button {
      margin-left: 16px;
    }
  }
  .search-table__footer {
    background: none;
    text-align: right;
  }
}
.comm-page-title {
  display: flex;
  align-items: center;
  // background-color: #fff;
  margin-top: 0;
  margin-bottom: 20px;
  .title-name {
    width: 50%;
    font-size: 14px;
    color: #303133;
    font-weight: bold;
    text-align: left;
    line-height: 14px;
    // border-left: 4px solid var(--color-primary);
    padding-left: 5px;
  }
}
.search-table_inner.search-table-content {
  .content-box {
    box-shadow: none;
  }
  .table-content {
    padding: 0px 0px 16px 0px;
  }
  .search-table__header {
    box-shadow: none;
  }
}
</style>
