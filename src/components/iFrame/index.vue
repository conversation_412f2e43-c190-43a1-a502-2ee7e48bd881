<template>
  <div v-loading="loading" :style="'height:' + height">
    <iframe
      ref="iframeRef"
      :src="url"
      frameborder="no"
      style="width: 100%; height: 100%"
      scrolling="auto"
    />
  </div>
</template>
<script>
export default {
  props: {
    src: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      height: document.documentElement.clientHeight - 94.5 + "px;",
      loading: true,
      url: this.src,
    };
  },
  methods: {
    pushChildInfo() {
      this.$nextTick(() => {
        let iframeRef = this.$refs.iframeRef;
        let that = this;
        iframeRef.onload = function () {
          iframeRef.contentWindow.postMessage(
            {
              type: "language",
              languageVal: that.getLanguage(),
            },
            iframeRef.src,
          );
          iframeRef.contentWindow.postMessage(
            {
              type: "theme",
              themeVal: that.$store.state.settings.theme,
            },
            iframeRef.src,
          );
        };
      });
    },
  },
  mounted: function () {
    setTimeout(() => {
      this.loading = false;
    }, 300);
    const that = this;
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 94.5 + "px;";
    };
    this.pushChildInfo();
  },
};
</script>
