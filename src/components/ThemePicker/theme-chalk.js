export const chalkStr = `@charset "UTF-8";

.el-pagination button:hover {
    color: #409EFF
}

.el-pagination__sizes .el-input .el-input__inner:hover {
    border-color: #409EFF
}

.el-pagination.is-background .el-pager li:not(.disabled):hover {
    color: #409EFF
}

.el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: #409EFF;
}

.el-pager li:hover {
    color: #409EFF
}

.el-pager li.active {
    color: #409EFF;
}

.el-dialog__headerbtn:focus .el-dialog__close,
.el-dialog__headerbtn:hover .el-dialog__close {
    color: #409EFF
}

.el-menu--horizontal>.el-submenu.is-active .el-submenu__title {
    border-bottom: 2px solid #409EFF;
}

.el-menu--horizontal>.el-menu-item.is-active {
    border-bottom: 2px solid #409EFF;
}

.el-menu-item.is-active {
    color: #409EFF
}

.el-submenu.is-active .el-submenu__title {
    border-bottom-color: #409EFF
}

.el-radio-button__inner:hover {
    color: #409EFF
}

.el-radio-button__orig-radio:checked+.el-radio-button__inner {
    background-color: #409EFF;
    border-color: #409EFF;
    -webkit-box-shadow: -1px 0 0 0 #409EFF;
    box-shadow: -1px 0 0 0 #409EFF
}

.el-radio-button:focus:not(.is-focus):not(:active):not(.is-disabled) {
    -webkit-box-shadow: 0 0 2px 2px #409EFF;
    box-shadow: 0 0 2px 2px #409EFF
}

.el-switch__label.is-active {
    color: #409EFF
}

.el-switch.is-checked .el-switch__core {
    border-color: #409EFF;
    background-color: #409EFF
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
    color: #409EFF;
}

.el-select-dropdown__item.selected {
    color: #409EFF;
}


.el-select .el-input__inner:focus {
    border-color: #409EFF
}

.el-range-editor.is-active,
.el-range-editor.is-active:hover,
.el-select .el-input.is-focus .el-input__inner {
    border-color: #409EFF
}

.el-table th.el-table__cell>.cell.highlight {
    color: #409EFF
}

.el-table .ascending .sort-caret.ascending {
    border-bottom-color: #409EFF
}

.el-table .descending .sort-caret.descending {
    border-top-color: #409EFF
}

.el-table-filter__list-item.is-active {
    background-color: #409EFF;
}

.el-date-table td.today span {
    color: #409EFF;
}

.el-date-table td.available:hover {
    color: #409EFF
}

.el-date-table td.current:not(.disabled) span {
    background-color: #409EFF
}

.el-date-table td.end-date span,
.el-date-table td.start-date span {
    background-color: #409EFF
}

.el-date-table td.selected span {
    background-color: #409EFF;
}

.el-month-table td.today .cell {
    color: #409EFF;
}


.el-month-table td .cell:hover {
    color: #409EFF
}


.el-month-table td.end-date .cell,
.el-month-table td.start-date .cell {
    background-color: #409EFF
}

.el-month-table td.current:not(.disabled) .cell {
    color: #409EFF
}


.el-year-table td.today .cell {
    color: #409EFF;
}

.el-year-table td .cell:hover,
.el-year-table td.current:not(.disabled) .cell {
    color: #409EFF
}

.el-date-picker__header-label.active,
.el-date-picker__header-label:hover {
    color: #409EFF
}

.time-select-item.selected:not(.disabled) {
    color: #409EFF;
}

.el-picker-panel__shortcut:hover {
    color: #409EFF
}

.el-picker-panel__shortcut.active {
    color: #409EFF
}

.el-picker-panel__icon-btn:hover {
    color: #409EFF
}

.el-time-spinner__arrow:hover {
    color: #409EFF
}

.el-time-panel__btn.confirm {
    color: #409EFF
}

.el-message-box__headerbtn:focus .el-message-box__close,
.el-message-box__headerbtn:hover .el-message-box__close {
    color: #409EFF
}

.el-breadcrumb__inner a:hover,
.el-breadcrumb__inner.is-link:hover {
    color: #409EFF;
}

.el-tabs__active-bar {
    background-color: #409EFF;
}

.el-tabs__new-tab:hover {
    color: #409EFF
}

.el-tabs__item:focus.is-active.is-focus:not(:active) {
    -webkit-box-shadow: 0 0 2px 2px #409EFF inset;
    box-shadow: 0 0 2px 2px #409EFF inset;
}

.el-tabs__item.is-active {
    color: #409EFF
}

.el-tabs__item:hover {
    color: #409EFF;
}

.el-tabs--border-card>.el-tabs__header .el-tabs__item.is-active {
    color: #409EFF;
}

.el-tabs--border-card>.el-tabs__header .el-tabs__item:not(.is-disabled):hover {
    color: #409EFF
}

.el-tree__drop-indicator {
    background-color: #409EFF
}

.el-tree-node.is-drop-inner>.el-tree-node__content .el-tree-node__label {
    background-color: #409EFF;
}

.el-input-number__decrease:hover,
.el-input-number__increase:hover {
    color: #409EFF
}

.el-input-number__decrease:hover:not(.is-disabled)~.el-input .el-input__inner:not(.is-disabled),
.el-input-number__increase:hover:not(.is-disabled)~.el-input .el-input__inner:not(.is-disabled) {
    border-color: #409EFF
}


.el-slider__bar {
    background-color: #409EFF;
}

.el-slider__button {
    border: 2px solid #409EFF;
}

.el-slider.is-vertical.el-slider--with-input .el-slider__input:active .el-input-number__decrease,
.el-slider.is-vertical.el-slider--with-input .el-slider__input:active .el-input-number__increase {
    border-color: #409EFF
}

.el-loading-spinner .el-loading-text {
    color: #409EFF;
}

.el-loading-spinner .path {
    stroke: #409EFF;
}

.el-loading-spinner i {
    color: #409EFF
}

.el-upload--picture-card:hover,
.el-upload:focus {
    border-color: #409EFF;
    color: #409EFF
}

.el-upload:focus .el-upload-dragger {
    border-color: #409EFF
}


.el-upload-dragger .el-upload__text em {
    color: #409EFF;
}

.el-upload-dragger:hover {
    border-color: #409EFF
}

.el-upload-dragger.is-dragover {
    border: 2px dashed #409EFF
}

.el-upload-list__item .el-icon-close-tip {
    color: #409EFF
}

.el-upload-list__item.is-success .el-upload-list__item-name:focus,
.el-upload-list__item.is-success .el-upload-list__item-name:hover {
    color: #409EFF;
}

.el-upload-list__item-delete:hover {
    color: #409EFF
}

.el-progress-bar__inner {
    background-color: #409EFF;
}

.el-badge__content--primary {
    background-color: #409EFF
}

.el-step__head.is-finish {
    color: #409EFF;
    border-color: #409EFF
}

.el-step__title.is-finish {
    color: #409EFF
}

.el-step__description.is-finish {
    color: #409EFF
}

.el-collapse-item__header.focusing:focus:not(:hover),
.el-tag {
    color: #409EFF
}

.el-tag.is-hit {
    border-color: #409EFF
}

.el-tag .el-tag__close {
    color: #409eff
}

.el-tag .el-tag__close:hover {
    background-color: #409eff
}


.el-tag--dark {
    background-color: #409eff;
    border-color: #409eff;
}

.el-tag--dark.is-hit {
    border-color: #409EFF
}

.el-tag--plain {
    color: #409eff
}

.el-tag--plain.is-hit {
    border-color: #409EFF
}

.el-tag--plain .el-tag__close {
    color: #409eff
}

.el-tag--plain .el-tag__close:hover {
    background-color: #409eff
}

.el-cascader .el-input .el-input__inner:focus,
.el-cascader .el-input.is-focus .el-input__inner {
    border-color: #409EFF
}

.el-cascader__suggestion-item.is-checked {
    color: #409EFF;
}

.el-color-predefine__color-selector.selected {
    -webkit-box-shadow: 0 0 3px 2px #409EFF;
    box-shadow: 0 0 3px 2px #409EFF
}

.el-color-dropdown__btn:hover {
    color: #409EFF;
    border-color: #409EFF
}

.el-color-dropdown__link-btn {
    color: #409EFF;
}

.el-color-dropdown__link-btn:hover {
    color: tint(#409EFF, 20%)
}

.el-textarea__inner:focus {
    border-color: #409EFF
}

.el-input.is-active .el-input__inner,
.el-input__inner:focus {
    border-color: #409EFF;
}

.el-timeline-item__node--primary,
.el-transfer__button {
    background-color: #409EFF
}

.el-transfer-panel__item:hover {
    color: #409EFF
}

.el-link.is-underline:hover:after {
    border-bottom: 1px solid #409EFF
}

.el-link.el-link--default:after,
.el-link.el-link--primary.is-underline:hover:after,
.el-link.el-link--primary:after {
    border-color: #409EFF
}


.el-link.el-link--default:hover {
    color: #409EFF
}

.el-link.el-link--primary {
    color: #409EFF
}

.el-button:focus,
.el-button:hover {
    color: #409EFF;
}

.el-button.is-plain:focus,
.el-button.is-plain:hover {
    border-color: #409EFF;
    color: #409EFF
}

.el-button--primary {
    background-color: #409EFF;
    border-color: #409EFF
}

.el-button--primary.is-plain {
    color: #409EFF;
}

.el-button--primary.is-plain:focus,
.el-button--primary.is-plain:hover {
    background: #409EFF;
    border-color: #409EFF;
}

.el-button--text {
    color: #409EFF;
}

.el-backtop,
.el-calendar-table td.is-today {
    color: #409EFF
}

.el-checkbox.is-bordered.is-checked {
    border-color: #409EFF
}

.el-checkbox.is-bordered.is-disabled {
    border-color: #EBEEF5;
    cursor: not-allowed
}

.el-checkbox.is-bordered.el-checkbox--medium {
    padding: 7px 20px 7px 10px;
    border-radius: 4px;
    height: 36px
}

.el-checkbox.is-bordered.el-checkbox--medium .el-checkbox__label {
    line-height: 17px;
    font-size: 14px
}

.el-checkbox.is-bordered.el-checkbox--medium .el-checkbox__inner {
    height: 14px;
    width: 14px
}

.el-checkbox.is-bordered.el-checkbox--small {
    padding: 5px 15px 5px 10px;
    border-radius: 3px;
    height: 32px
}

.el-checkbox.is-bordered.el-checkbox--small .el-checkbox__label {
    line-height: 15px;
    font-size: 12px
}

.el-checkbox.is-bordered.el-checkbox--small .el-checkbox__inner {
    height: 12px;
    width: 12px
}

.el-checkbox.is-bordered.el-checkbox--small .el-checkbox__inner::after {
    height: 6px;
    width: 2px
}

.el-checkbox.is-bordered.el-checkbox--mini {
    padding: 3px 15px 3px 10px;
    border-radius: 3px;
    height: 28px
}

.el-checkbox.is-bordered.el-checkbox--mini .el-checkbox__label {
    line-height: 12px;
    font-size: 12px
}

.el-checkbox.is-bordered.el-checkbox--mini .el-checkbox__inner {
    height: 12px;
    width: 12px
}

.el-checkbox.is-bordered.el-checkbox--mini .el-checkbox__inner::after {
    height: 6px;
    width: 2px
}

.el-checkbox__input {
    cursor: pointer;
    outline: 0;
    line-height: 1;
    vertical-align: middle
}

.el-checkbox__input.is-disabled .el-checkbox__inner {
    background-color: #edf2fc;
    border-color: #DCDFE6;
    cursor: not-allowed
}

.el-checkbox__input.is-disabled .el-checkbox__inner::after {
    cursor: not-allowed;
    border-color: #C0C4CC
}

.el-checkbox__input.is-disabled .el-checkbox__inner+.el-checkbox__label {
    cursor: not-allowed
}

.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #F2F6FC;
    border-color: #DCDFE6
}

.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
    border-color: #C0C4CC
}

.el-checkbox__input.is-disabled.is-indeterminate .el-checkbox__inner {
    background-color: #F2F6FC;
    border-color: #DCDFE6
}

.el-checkbox__input.is-disabled.is-indeterminate .el-checkbox__inner::before {
    background-color: #C0C4CC;
    border-color: #C0C4CC
}

.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #409EFF;
    border-color: #409EFF
}

.el-checkbox__input.is-checked+.el-checkbox__label {
    color: #409EFF
}

.el-checkbox__input.is-focus .el-checkbox__inner {
    border-color: #409EFF
}

.el-checkbox__inner:hover {
    border-color: #409EFF
}

.el-checkbox-button__inner:hover {
    color: #409EFF
}

.el-checkbox-button.is-checked .el-checkbox-button__inner {
    background-color: #409EFF;
    border-color: #409EFF;
}

.el-checkbox-button.is-checked:first-child .el-checkbox-button__inner {
    border-left-color: #409EFF
}

.el-checkbox-button.is-focus .el-checkbox-button__inner {
    border-color: #409EFF
}

.el-radio.is-bordered.is-checked {
    border-color: #409EFF
}

.el-radio__input.is-checked .el-radio__inner {
    border-color: #409EFF;
    background: #409EFF
}

.el-radio__input.is-checked+.el-radio__label {
    color: #409EFF
}

.el-radio__input.is-focus .el-radio__inner {
    border-color: #409EFF
}

.el-radio__inner:hover {
    border-color: #409EFF
}

.el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
    -webkit-box-shadow: 0 0 2px 2px #409EFF;
    box-shadow: 0 0 2px 2px #409EFF
}

.el-cascader-node.in-active-path,
.el-cascader-node.is-active,
.el-cascader-node.is-selectable.in-checked-path {
    color: #409EFF;
}`;
