import { getDn } from "@/api/register";
export default {
  data() {
    return {
      pluginInstalled: false,
    };
  },
  methods: {
    // 读取ukey
    onUkeyClick() {
      // if (!this.pluginInstalled) {
      //   this.$message.error(this.$t('validate.installUk'));
      //   return false;
      // }
      if (!this.$ukey.isUKeyExist()) {
        this.$message.error(this.$t("validate.insertUk"));
        return;
      }
      this.dialogVisible = true;
    },
    // 弹窗关闭
    eventClose() {
      this.dialogVisible = false;
    },
    eventSubmit(pin) {
      // let str = this.$ukey.login(pin);
      // if ("184549392" === str) {
      //   this.$message.error(this.$t('validate.checkUKeyType'))
      //   return false;
      // }
      // if (0 !== str) {
      //     this.$message.error(this.$t('validate.uKeyPasswordError'))
      //     return false;
      // }
      let devName = this.$ukey.getContainerName();
      let cert = this.$ukey.getCert(devName);
      this.userForm.userCert = cert;
      getDn(cert)
        .then((res) => {
          if (res.data == undefined || res.data == "") {
            this.$message.error(this.$t("validate.uSBKeyCertEmpty"));
            this.userForm.userCert = "";
            return false;
          }
          this.userForm.userDn = res.data;
        })
        .catch(() => {
          this.userForm.userCert = "";
        });
    },
    // 获取序列号
    getSerial() {
      return this.$ukey.getUKeySerial();
    },
    getVersion() {
      this.$ukey.checkPlugin((version) => {
        if (version.length) {
          this.pluginInstalled = true;
        } else {
          this.pluginInstalled = false;
        }
      });
    },
  },
  // created() {
  //   this.getVersion()
  // },
};
