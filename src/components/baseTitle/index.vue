<template>
  <div class="base-title">
    {{ title }}
  </div>
</template>
<script>
export default {
  name: "baseTitle",
  props: {
    title: {
      type: String,
      default: "",
    },
  },
};
</script>
<style lang="scss" scoped>
.base-title {
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 16px;
  line-height: 15px;
  display: flex;
  align-items: center;
  // &::before {
  //   content: "";
  //   display: inline-block;
  //   width: 4px;
  //   height: 15px;
  //   background: var(--color-primary);
  //   margin-right: 4px;
  // }
}
</style>
