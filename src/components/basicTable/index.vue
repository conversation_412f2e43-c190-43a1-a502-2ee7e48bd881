<template>
  <el-table
    :data="tableData"
    :border="border"
    :stripe="stripe"
    tooltip-effect="light"
  >
    <el-table-column
      v-for="item in columns"
      :key="item.name"
      :label="item.label"
      :prop="item.prop"
      :width="item.width"
      :align="item.align"
      :show-overflow-tooltip="item.tooltip === false ? false : true"
    >
      <template v-if="item.render" #default="{ row, $index }">
        <slot :name="item.render" :data="row" :index="$index"></slot>
      </template>
      <div slot="empty" class="empty-container">
        <sw-nodata :title="emptyText" v-if="!$slots.empty" />
        <slot name="empty"> </slot>
      </div>
    </el-table-column>
  </el-table>
</template>
<script>
export default {
  name: "basicTable",
  props: {
    tableData: {
      type: Array,
      default: () => [],
    },
    columns: {
      type: Array,
      default: () => [],
    },
    border: {
      type: Boolean,
      default: true,
    },
    stripe: {
      type: Boolean,
      default: true,
    },
    emptyText: {
      type: String,
      default: "",
    },
  },
  data() {
    return {};
  },
};
</script>
