import router from "@/router/index.js";
export default {
  methods: {
    onJumpPage(routeQuery, routeName, params) {
      let query = {};
      if (Object.keys(routeQuery).length > 0) {
        Object.keys(routeQuery).forEach((item) => {
          query[`parent-${item}`] = routeQuery[item];
        });
      }
      router.push({
        path: routeName,
        query: {
          ...params,
          ...query,
        },
      });
    },
    onBackPage(jumpPath, routeQuery) {
      if (jumpPath) {
        let query = {};
        if (routeQuery.parentQuery) {
          Object.keys(routeQuery).forEach((item) => {
            if (item !== "parentQuery" && item.indexOf("parent") > -1) {
              query[item.split("-")[1]] = routeQuery[item];
            }
          });
        }
        router.push({
          path: jumpPath,
          query,
        });
      } else {
        router.back();
      }
    },
  },
};
