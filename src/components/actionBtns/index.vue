<template>
  <div class="actions-container">
    <el-button
      v-for="(item, index) in btns.filter(
        (item) => !(item.hidden && item.hidden()),
      )"
      :key="index"
      :icon="item.icon"
      type="primary"
      class="action-btn"
      :class="item.class"
      @click="item.func"
      :id="item.id"
      :disabled="item.rule ? item.rule() : false"
      v-hasPermi="item.permi"
      >{{ item.name }}</el-button
    >
  </div>
</template>
<script>
export default {
  name: "actionBtns",
  props: {
    btns: {
      type: Array,
      default: () => [],
    },
  },
};
</script>
<style lang="scss" scoped>
.actions-container {
  display: flex;
}
</style>
