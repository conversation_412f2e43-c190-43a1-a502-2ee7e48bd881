<template>
  <div>
    <svg-icon
      @click="onChangeLanguage(language == 'zh' ? 'en' : 'zh')"
      v-if="showType == 'icon'"
      :icon-class="isZh() ? 'en' : 'zh'"
    />
    <div class="list-btn" v-if="showType == 'list'">
      <span v-for="(i, idx) in languageList" :key="i.value">
        <el-button
          type="text"
          @click="onChangeLanguage(i.value)"
          :disabled="i.value == language"
        >
          {{ i.name }}
        </el-button>
        <el-divider
          v-if="idx < languageList.length - 1"
          direction="vertical"
        ></el-divider>
      </span>
    </div>
    <div class="dropdown-btn" v-if="showType == 'dropdown'">
      <el-dropdown trigger="hover" @command="onChangeLanguage">
        <span class="el-dropdown-link">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            fill="none"
            version="1.1"
            width="16"
            height="16"
            viewBox="0 0 16 16"
          >
            <defs>
              <clipPath id="master_svg0_44_4470/41_827">
                <rect x="0" y="0" width="16" height="16" rx="0" />
              </clipPath>
            </defs>
            <g clip-path="url(#master_svg0_44_4470/41_827)">
              <g>
                <g>
                  <g>
                    <path
                      d="M7.9996678125,14.6664078125C4.3176678125,14.6664078125,1.3330078125,11.6816078125,1.3330078125,7.9996678125C1.3330078125,4.3176678125,4.3176678125,1.3330078125,7.9996678125,1.3330078125C11.6816078125,1.3330078125,14.6664078125,4.3176678125,14.6664078125,7.9996678125C14.6664078125,11.6816078125,11.6816078125,14.6664078125,7.9996678125,14.6664078125ZM6.4730078125,13.1110078125C5.8152878125,11.7159078125,5.4342578125,10.2064478125,5.3510078125,8.6663478125L2.7076678125,8.6663478125C2.9727178125,10.7624478125,4.4489778125,12.5050078125,6.4730078125,13.1110078125ZM6.6863478125,8.6663478125C6.7870078125,10.2923678125,7.2516678125,11.8196078125,7.9996678125,13.1676078125C8.7678878125,11.7840078125,9.2166378125,10.2459678125,9.3130078125,8.6663478125L6.6863478125,8.6663478125ZM13.2916078125,8.6663478125L10.6483678125,8.6663478125C10.5650878125,10.2064478125,10.1840478125,11.7159078125,9.5263678125,13.1110078125C11.5504078125,12.5050078125,13.0266078125,10.7624478125,13.2916078125,8.6663478125ZM2.7076678125,7.3330078125L5.3510078125,7.3330078125C5.4342578125,5.7928678125,5.8152878125,4.2834678125,6.4730078125,2.8883478125C4.4489778125,3.4943078125,2.9727178125,5.2369078125,2.7076678125,7.3330078125ZM6.6870078125,7.3330078125L9.3123478125,7.3330078125C9.2161778125,5.7534078125,8.767657812500001,4.215357812500001,7.9996678125,2.8316678125C7.2314678125,4.2153078125,6.7827078125,5.7533478125,6.6870078125,7.3330078125L6.6870078125,7.3330078125ZM9.5263678125,2.8883478125C10.1840478125,4.2834678125,10.5650878125,5.7928678125,10.6483678125,7.3330078125L13.2916078125,7.3330078125C13.0266078125,5.2369078125,11.5504078125,3.4943078125,9.5263678125,2.8883478125Z"
                      fill="currentColor"
                      fill-opacity="1"
                      style="mix-blend-mode: passthrough"
                    />
                  </g>
                </g>
              </g>
            </g>
          </svg>
          <span class="lang">{{ language == "en" ? "English" : "中文" }}</span>
          <i class="el-icon-arrow-down el-icon--right"></i>
        </span>

        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            v-for="(item, idx) in languageList"
            :key="item.value"
            :command="item.value"
          >
            {{ item.name }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    showType: {
      type: String,
      default: "icon",
    },
  },
  data() {
    return {
      languageList: [
        {
          name: "中文",
          value: "zh",
        },
        {
          name: "English",
          value: "en",
        },
      ],
      language: this.getLanguage() || "en",
    };
  },
  methods: {
    onChangeLanguage(item) {
      if (this.$i18n) {
        this.$i18n.locale = item;
      }
      this.setLanguage(item);
      // 需要往子项目中传入切换的值
      // const existIframe = document.getElementsByTagName("iframe");
      // for (let i = 0; i < existIframe.length; i += 1) {
      //   existIframe[i].contentWindow.postMessage(
      //     {
      //       type: "language",
      //       languageVal: item,
      //     },
      //     existIframe[i].src,
      //   );
      // }
      location.reload();
    },
  },
};
</script>

<style lang="scss" scoped>
.list-btn {
  font-size: 14px;
  .el-button {
    padding: 0px 8px;
  }
}
.dropdown-btn {
  cursor: pointer;
  display: inline;
  .el-dropdown {
    color: #cccccc;
    margin-top: 2px;
    .el-dropdown-link {
      display: flex;
      align-items: center;
      .lang {
        padding: 0px 5px;
      }
    }
  }
}
</style>
