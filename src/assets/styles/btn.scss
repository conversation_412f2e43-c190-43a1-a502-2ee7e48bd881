@import "./variables.scss";

@mixin colorBtn($color) {
  background: $color;

  &:hover {
    color: $color;

    &:before,
    &:after {
      background: $color;
    }
  }
}

/**
*  按钮样式  icon
*/
.action-btn.el-button {
  height: 32px;
  line-height: 32px;
  padding: 0px 16px;
  font-size: 14px;
  display: flex;
  align-items: center;
  i {
    font-size: 14px;
    margin-right: 4px;
  }
  &:has(i) {
    padding: 0px 12px 0 12px;
  }
  &::before {
    font-size: 14px;
    margin-right: 4px;
  }
}

// 统一设置按钮禁止样式
.el-button.action-btn.is-disabled,
.el-button.action-btn.is-disabled:hover,
.el-button.action-btn.is-disabled:focus,
.el-button.action-btn.is-disabled:active {
  background-color: $white;
  border-color: #e6ebf5;
  color: #c0c4cc;
  i {
    color: #c0c4cc;
  }
}

@mixin actionBtnColor($color, $color1) {
  border-color: #bbbbbb;
  color: #444;
  span {
    line-height: 20px;
  }

  i {
    color: $color;
  }

  &:focus {
    border-color: #bbbbbb;
    color: #444;
    background-color: $white;

    i {
      color: $color;
    }
  }

  &:hover {
    border-color: #bbbbbb;
    color: #444;
    background-color: $color1;
    i {
      color: $color;
    }
  }

  &:active {
    background-color: $color;
    border-color: #bbbbbb;
    color: $white;

    i {
      color: $white;
    }
  }
}

@mixin actionBtnColor1($color, $color1) {
  border-color: #bbbbbb;
  background: #fff;
  color: rgba(0, 0, 0, 0.65);
  span {
    line-height: 20px;
  }

  i {
    padding-right: 4px;
    color: rgba(0, 0, 0, 0.65);
  }

  &:focus {
    border-color: #bbbbbb;
    color: rgba(0, 0, 0, 0.65);
    background-color: #fff;
    i {
      color: rgba(0, 0, 0, 0.65);
    }
  }

  &:hover {
    border-color: $color;
    color: $color;
    background-color: $color1;
    i {
      color: $color;
    }
  }

  &:active {
    border-color: $color;
    color: $color;
    background-color: $color1;

    i {
      color: $color;
    }
  }
}

.el-button.green-color-btn {
  // @include actionBtnColor($green, $light-green);
}

.el-button.blue-color-btn {
  @include actionBtnColor(var(--color-primary), var(--color-primary19));
}

.el-button.red-color-btn {
  //   @include actionBtnColor($red, $light-red);
}

.el-button.el-button--primary.white-color-btn {
  @include actionBtnColor1(var(--color-primary), var(--color-primary19));
}

/**
* 文本按钮
*/

@mixin textBtnColor($color) {
  color: $color;

  &:hover {
    color: $color;
    text-decoration: underline;
  }

  &:active,
  &:focus {
    color: $color;
  }
  &.is-disabled {
    color: #c0c4cc;
  }
}
.el-button--text.oper-text-btn {
  @include textBtnColor(var(--color-primary));
}

.el-button--text.delete-text-btn {
  @include textBtnColor($red);
}

/**
* 提交、取消按钮
*/
.oper-btn.el-button {
  width: 120px;
  height: 32px;
  padding: 0;
}

.el-button.el-button--primary {
  border-color: var(--color-primary);
  background: var(--color-primary);
  color: $white;
  &:focus {
    border-color: var(--color-primary);
    background: var(--color-primary);
    color: $white;
  }
  &:hover {
    border-color: var(--color-primary89);
    background: var(--color-primary89);
    color: $white;
  }

  &:active {
    border-color: var(--color-primary);
    background: var(--color-primary);
    color: $white;
  }
}

.el-button.el-button--primary.is-disabled {
  border-color: #e6ebf5;
  &:focus {
    border-color: #e6ebf5;
  }
  &:hover {
    border-color: #e6ebf5;
  }

  &:active {
    border-color: #e6ebf5;
  }
}
.el-button.oper-btn.is-disabled,
.el-button.oper-btn.is-disabled:hover,
.el-button.oper-btn.is-disabled:focus,
.el-button.oper-btn.is-disabled:active {
  color: #c0c4cc;
  cursor: not-allowed;
  border-color: #e6ebf5;
  background-color: #fff;
}

.el-button.cancel-btn {
  border-color: #bbbbbb;
  background-color: $white;

  &:focus {
    border-color: #bbbbbb;
    background-color: $white;
    color: #606266;
  }

  &:hover {
    border-color: var(--color-primary);
    background-color: $white;
    color: var(--color-primary);
  }

  &:active {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
    color: $white;
  }
}

/**
* 非icon样式
*/
.drop-btn {
  color: var(--color-primary);

  &:hover {
    color: var(--color-primary);
  }
}

.drop-delete {
  color: $red;

  &:hover {
    color: $red;
  }
}

/* button color */
.el-button--cyan.is-active,
.el-button--cyan:active {
  background: $cyan;
  border-color: $cyan;
  color: $white;
}

.el-button--cyan:focus,
.el-button--cyan:hover {
  background: $light-cyan;
  border-color: $light-cyan;
  color: $white;
}

.el-button--cyan {
  background-color: $cyan;
  border-color: $cyan;
  color: $white;
}

.el-button {
  font-size: 14px;
}

/**
* 纯图标按钮
*/
@mixin iconBtnColor($color) {
  color: $color;

  &:hover {
    color: $color;
  }

  &:active,
  &:focus {
    color: $color;
  }
  &.is-disabled {
    color: #c0c4cc;
  }
}
.icon-btn-box .el-icon-circle-plus {
  @include iconBtnColor($green);
}

.icon-btn-box .el-icon-remove {
  @include iconBtnColor($red);
}
