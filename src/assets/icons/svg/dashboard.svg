<svg xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="200" height="200" viewBox="0 0 200 200">
    <defs>
        <clipPath id="master_svg0_11_26888">
            <rect x="0" y="0" width="200" height="200" rx="0"/>
        </clipPath>
    </defs>
    <g clip-path="url(#master_svg0_11_26888)">
        <g>
            <g>
                <path d="M42.8578,121.3815C42.8578,117.4627,41.4625,114.1157,38.6719,111.3438C35.8813,108.5703,32.5156,107.1844,28.5719,107.1844C24.6281,107.1844,21.2609,108.5703,18.4703,111.3438C15.6812,114.1157,14.2859,117.4627,14.2859,121.3815C14.2859,125.3,15.6812,128.647,18.4703,131.41899999999998C21.2609,134.19299999999998,24.6266,135.579,28.5719,135.579C32.5156,135.579,35.8812,134.19299999999998,38.6719,131.41899999999998C41.4625,128.64600000000002,42.8578,125.3,42.8578,121.3815ZM64.2859,71.69149999999999C64.2859,67.7727,62.8906,64.42580000000001,60.1,61.6538C57.3109,58.8804,53.9438,57.4944,50,57.4944C46.0563,57.4944,42.6891,58.8803,39.9,61.6538C37.1094,64.42580000000001,35.7141,67.7727,35.7141,71.69149999999999C35.7141,75.6103,37.1094,78.9573,39.9,81.72919999999999C42.6891,84.50110000000001,46.0563,85.8887,50,85.8887C53.9437,85.8887,57.3109,84.50110000000001,60.1,81.72919999999999C62.8906,78.95570000000001,64.2859,75.6103,64.2859,71.69149999999999ZM112.053,125.041L123.327,82.6714C123.772,80.7495,123.494,78.95570000000001,122.489,77.2916C121.484,75.6291,120.052,74.5385,118.192,74.0197C116.333,73.5041,114.547,73.7431,112.834,74.7416C111.123,75.74000000000001,110.006,77.1994,109.486,79.1229L98.2141,121.4924C93.75,121.8627,89.7688,123.471,86.2719,126.318C82.775,129.164,80.4312,132.805,79.2406,137.243C77.7531,142.937,78.4969,148.334,81.4734,153.437C84.45,158.538,88.8016,161.828,94.5312,163.307C100.261,164.787,105.692,164.048,110.827,161.088C115.959,158.132,119.27,153.807,120.759,148.112C121.95,143.674,121.727,139.351,120.089,135.135C118.453,130.921,115.773,127.557,112.053,125.041ZM185.714,121.3815C185.714,117.4627,184.319,114.1157,181.53,111.3438C178.739,108.5703,175.373,107.1844,171.428,107.1844C167.484,107.1844,164.119,108.5703,161.328,111.3438C158.537,114.1157,157.142,117.4627,157.142,121.3815C157.142,125.3,158.537,128.647,161.328,131.41899999999998C164.119,134.19299999999998,167.484,135.579,171.428,135.579C175.372,135.579,178.739,134.19299999999998,181.53,131.41899999999998C184.319,128.64600000000002,185.714,125.3,185.714,121.3815ZM114.286,50.3958C114.286,46.4754,112.891,43.1301,110.1,40.3566C107.312,37.5862,103.944,36.1987,100,36.1987C96.0563,36.1987,92.6891,37.5847,89.9,40.3566C87.1094,43.1301,85.7141,46.4754,85.7141,50.3958C85.7141,54.3146,87.1094,57.6616,89.9,60.4335C92.6891,63.2054,96.0562,64.593,100,64.593C103.944,64.593,107.311,63.2054,110.1,60.4335C112.891,57.66,114.286,54.3146,114.286,50.3958ZM164.286,71.69149999999999C164.286,67.7727,162.891,64.42580000000001,160.1,61.6538C157.311,58.8804,153.944,57.4944,150,57.4944C146.056,57.4944,142.689,58.8804,139.9,61.6538C137.109,64.42580000000001,135.714,67.7727,135.714,71.69149999999999C135.714,75.6104,137.109,78.9573,139.9,81.72919999999999C142.689,84.50110000000001,146.056,85.8887,150,85.8887C153.944,85.8887,157.311,84.50110000000001,160.1,81.72919999999999C162.891,78.95570000000001,164.286,75.6103,164.286,71.69149999999999ZM200,121.3815C200,140.68,194.755,158.538,184.264,174.954C182.85,177.098,180.842,178.17,178.236,178.17L21.7656,178.17C19.1609,178.17,17.1516,177.098,15.7375,174.954C5.24531,158.612,0,140.755,0,121.3815C0,107.9234,2.64062,95.0576,7.92344,82.7824C13.2062,70.5087,20.3125,59.9335,29.2406,51.061499999999995C38.1703,42.1863,48.8094,35.1252,61.1609,29.875140000000002C73.5125,24.62505,86.4578,22,100,22C113.542,22,126.487,24.62505,138.839,29.875140000000002C151.191,35.1252,161.83,42.1879,170.759,51.061499999999995C179.688,59.9335,186.794,70.5087,192.077,82.78229999999999C197.358,95.0576,200,107.9234,200,121.3815Z" fill="currentColor" fill-opacity="1" style="mix-blend-mode:passthrough"/>
            </g>
        </g>
    </g>
</svg>