import defaultSettings from "@/settings";
import logoImg from "@/assets/images/sysMiniLogo.svg"; // 默认图标（左）
import titleImg from "@/assets/images/sysLogo.svg"; // 默认图片标题（右）
import defaultLogo from "@/assets/logo/logo.svg"; // 默认登录页logo
import defaultLeftLogo from "@/assets/logo/login-logo.png"; // 默认登录页左封面
import themeSystem from "@/utils/themeSystem.js"; // 默认系统主题[]

const {
  webTitle,
  sideTheme,
  showSettings,
  topNav,
  tagsView,
  fixedHeader,
  sidebarLogo,
  dynamicTitle,
  menuColorCustom,
  menuColorActiveCustom,
  menuBackgroundCustom,
  subMenuBackgroundActiveCustom,
  subMenuBackgroundCustom,
  subMenuHoverCustom,
  topBackgroundCustom,
  topSvgCustom,
  titleLogo,
  isLogoIcon,
  isTitleLogo,
  titleLogoColor,
  titleLogoFontStyle,
  titleLogoFontSize,
} = defaultSettings;

const storageSetting = JSON.parse(localStorage.getItem("layout-setting")) || "";
const logoSetting = JSON.parse(localStorage.getItem("logo-setting")) || "";

const state = {
  title: "",
  webTitle: webTitle || "",
  themeSystem: themeSystem || [],
  isCustom: JSON.parse(localStorage.getItem("isCustom")) || false, // 是否自定义主题
  themeNameZh: storageSetting.themeNameZh || "简约风格",
  themeNameEn: storageSetting.themeNameEn || "Concise Style",
  theme: storageSetting.theme || "#1C6CDD",
  navBackground: storageSetting.navBackground || " var(--color-primary19)",
  navBorder: storageSetting.navBorder || "#1C6CDD",
  sideTheme: storageSetting.sideTheme || sideTheme,
  showSettings: showSettings,
  topNav: storageSetting.topNav === undefined ? topNav : storageSetting.topNav,
  tagsView:
    storageSetting.tagsView === undefined ? tagsView : storageSetting.tagsView,
  fixedHeader:
    storageSetting.fixedHeader === undefined
      ? fixedHeader
      : storageSetting.fixedHeader,
  sidebarLogo:
    storageSetting.sidebarLogo === undefined
      ? sidebarLogo
      : storageSetting.sidebarLogo,
  dynamicTitle:
    storageSetting.dynamicTitle === undefined
      ? dynamicTitle
      : storageSetting.dynamicTitle,
  // 侧边栏
  menuColorCustom: storageSetting.menuColorCustom || menuColorCustom,
  menuBackgroundCustom:
    storageSetting.menuBackgroundCustom || menuBackgroundCustom,
  menuColorActiveCustom:
    storageSetting.menuColorActiveCustom || menuColorActiveCustom,
  subMenuBackgroundActiveCustom:
    storageSetting.subMenuBackgroundActiveCustom ||
    subMenuBackgroundActiveCustom,
  subMenuBackgroundCustom:
    storageSetting.subMenuBackgroundCustom || subMenuBackgroundCustom,
  subMenuHoverCustom: storageSetting.subMenuHoverCustom || subMenuHoverCustom,
  // 顶部
  topBackgroundCustom:
    storageSetting.topBackgroundCustom || topBackgroundCustom,
  topSvgCustom: storageSetting.topSvgCustom || topSvgCustom,
  // 自定义LOGO
  logoIcon: logoSetting.logoIcon || logoImg, // 小logo图标(左)
  isTitleLogo: logoSetting.isTitleLogo || isTitleLogo, // 标题是否为文字
  titleLogo: logoSetting.titleLogo || titleLogo, // 文字标题
  titleLogoColor: logoSetting.titleLogoColor || titleLogoColor,
  titleLogoFontStyle: logoSetting.titleLogoFontStyle || titleLogoFontStyle,
  imageLogo: logoSetting.imageLogo || titleImg, //图片标题（右）
  loginLogo: logoSetting.loginLogo || defaultLogo, // 登录页logo
  loginCover: logoSetting.loginCover || defaultLeftLogo, // 登录页封面
};
const mutations = {
  CHANGE_SETTING: (state, { key, value }) => {
    if (state.hasOwnProperty(key)) {
      state[key] = value;
    }
  },
};

const actions = {
  // 修改布局设置
  changeSetting({ commit }, data) {
    commit("CHANGE_SETTING", data);
  },
  // 设置网页标题
  setTitle({ commit }, title) {
    state.title = title;
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
