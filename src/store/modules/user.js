import { loginUser, loginLdap, loginUk, loginDingDing, logout } from "@/api/login";
import { getInfo } from "@/api/user";
import { getToken, setToken, removeToken } from "@/utils/auth";
import { removeLanguage } from "@/utils/language";
import { getOperAuth } from "@/api/menu";
import { setSession, removeSession, clearSession } from "@/utils/session";

const user = {
  state: {
    token: getToken(),
    name: "",
    avatar: "",
    roles: [],
    permissions: [],
    userInfo: {},
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token;
    },
    SET_NAME: (state, name) => {
      state.name = name;
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles;
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions;
    },
    SET_USERINFO: (state, user) => {
      state.userInfo = user;
    },
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        let request;
        switch (userInfo.type) {
          case "user":
            request = loginUser;
            break;
          case "ldap":
            request = loginLdap;
            break;
          case "uk":
            request = loginUk;
            break;
          case "dingding":
            request = loginDingDing;
            break;
        }
        request(userInfo)
          .then((res) => {
            if (typeof res.data == "object") {
              setToken(res.data.token);
              commit("SET_TOKEN", res.data.token);
            } else {
              setToken(res.data);
              commit("SET_TOKEN", res.data);
            }
            if (userInfo.type == "uk") {
              setSession("isUkLogin", true);
            }
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    //   获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        // 被嵌入，获取权限标识， 否则获取用户信息 + 权限标识
        if (swGlobal.isInIframe == "true") {
          Promise.all([getOperAuth()])
            .then((values) => {
              let permissions = [];
              let permissionList = values[0].data;
              if (permissionList && permissionList.length > 0) {
                permissionList.forEach((item) => {
                  permissions.push(item.menuMark);
                });
              }
              commit("SET_PERMISSIONS", permissions);
              commit("SET_ROLES", ["a", "b"]);

              resolve(values);
            })
            .catch((err) => {
              reject(err);
            });
        } else {
          Promise.all([getInfo(), getOperAuth()])
            .then((values) => {
              let user = values[0].data;
              if (user.roleNames) {
                // 验证返回的roles是否是一个非空数组
                commit("SET_ROLES", user.roleNames.split(","));
              } else {
                commit("SET_ROLES", ["ROLE_DEFAULT"]);
              }
              user["userType"] = "1";
              commit(
                "SET_NAME",
                user.aliasName === null ? user.userName : user.aliasName
                // user.userType == "1" ? user.userName : user.userDn,
              );
              commit("SET_USERINFO", user);

              let permissions = [];
              let permissionList = values[1].data;
              if (permissionList && permissionList.length > 0) {
                permissionList.forEach((item) => {
                  permissions.push(item.menuMark);
                });
              }
              commit("SET_PERMISSIONS", permissions);

              resolve(values);
            })
            .catch((err) => {
              reject(err);
            });
        }
      });
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token)
          .then(() => {
            // removeToken();
            // removeLanguage();
            commit("SET_TOKEN", "");
            commit("SET_ROLES", []);
            commit("SET_PERMISSIONS", []);
            commit("SET_SYS_VERSION", "");
            removeToken();
            // removeSession("isLogin");
            // removeSession("isRecovery");
            // removeSession("isUkLogin");
            // removeSession("isUkReLoginDig");
            // removeSession("ukName");
            // removeSession("isAddUk");
            // removeSession("UKeyInsertStatus");
            clearSession();
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise((resolve) => {
        commit("SET_TOKEN", "");
        removeToken();
        // removeSession("isLogin");
        // removeSession("isRecovery");
        commit("SET_ROLES", []);
        commit("SET_PERMISSIONS", []);
        commit("SET_SYS_VERSION", "");
        // removeSession("isUkLogin");
        // removeSession("isUkReLoginDig");
        // removeSession("ukName");
        // removeSession("isAddUk");
        // removeSession("UKeyInsertStatus");
        clearSession();
        resolve();
      });
    },
  },
};

export default user;
