import SKF from "./skf";
var skf = null; //new SKF("swskfapi");

class UKeyApi {
  constructor(ukeyType = "swskfapi") {
    console.log(ukeyType);
    if (skf == null) {
      skf = new SKF(ukeyType);
    }
  }
}
//获取版本号
UKeyApi.prototype.getVersion = function () {
  return skf.GetVersion();
};

UKeyApi.prototype.checkPlugin = function (callback) {
  skf.GetVersionAsync(callback);
};

/**
 * 枚举设备
 * @returns {any|{msg: SKF.SKF_EnumDev._xhr.statusText, status: SKF.SKF_EnumDev._xhr.status}|{msg: SKF.SKF_EnumDev._xhr.statusText, status: SKF.SKF_EnumDev._xhr.status}}
 */
UKeyApi.prototype.enumDev = function () {
  return skf.SKF_EnumDev();
};

/**
 * 连接设备 返回连接句柄连接失败返回 null
 * @returns {string|null|SKF.SKF_ConnectDev._xhr.statusText|SKF.SKF_ConnectDev._xhr.statusText|*}
 */
UKeyApi.prototype.connectDev = function () {
  var hDev = sessionStorage.getItem("hDev");
  if (hDev !== "" && hDev !== undefined && hDev !== null) {
    var rv_1 = skf.SKF_GenRandom(hDev, 1);
    if (rv_1.status === 0) {
      return hDev;
    }
  }
  var rv_2 = skf.SKF_EnumDev();
  if (rv_2.status !== 0) {
    return null;
  }
  var rv = skf.SKF_ConnectDev(rv_2.value[0]);
  if (rv.status === 0) {
    sessionStorage.setItem("hDev", rv.value);
    return rv.value;
  }
  return null;
};

//获取设备信息 //注释待补充
UKeyApi.prototype.getDevInfo = function () {
  var hDev = this.connectDev();
  if (hDev == null) {
    return "";
  }
  var rv = skf.SKF_GetDevInfo(hDev);
  if (rv.status === 0) {
    return rv.value;
  }
  return "";
};

// 断开uk连接
UKeyApi.prototype.disConnectDev = function () {
  var hDev = this.connectDev();
  if (hDev == null) {
    return "";
  }
  var rv = skf.SKF_DisConnectDev(hDev);
  if (rv.status === 0) {
    return rv.value;
  }
  return "";
};

/*//设置label
function setLabel() {
    // var rv = skf.SKF_SetLabel(document.getElementById("t_connectdev").value, document.getElementById("t_setlabel").value);
    // document.getElementById("s_setlabel").innerText = rv.status;
}*/

/**
 * 修改UkeyPin
 * string oldPin 旧 PIN，最大长度为 16
 * string newPin 新 PIN，最大长度为 16
 * int pinType PIN 类型 0:管理员 PIN 1:用户 PIN
 */
UKeyApi.prototype.changePin = function (oldPin, newPin, pinType) {
  var hDev = this.connectDev();
  if (hDev == null) {
    return -1;
  }
  var rv = skf.SKF_ChangePIN(hDev, oldPin, newPin, pinType);
  return rv.status;
};

/**
 * 校验PIN
 * @param verifyPin PIN，最大长度为 16
 * @param pinType 0:管理员 PIN 1:用户 PIN
 * @returns {number} 0成功
 */
UKeyApi.prototype.verifyPin = function (verifyPin, pinType) {
  var hDev = this.connectDev();
  if (hDev == null) {
    return -1;
  }
  var rv = skf.SKF_VerifyPIN(hDev, verifyPin, pinType);
  return rv.status;
};

/*//解锁PIN
function unblockPin() {
    var rv = skf.SKF_UnblockPIN(document.getElementById("t_connectdev").value, document.getElementById("t_adminpin").value, document.getElementById("t_userpin").value);
    document.getElementById("s_unblockPin").innerText = rv.status;
}*/

/**
 * 写入文件
 * @param fileName 文件名称
 * @param data 数据
 * @returns {SKF.SKF_WriteFile._xhr.status|SKF.SKF_WriteFile._xhr.status|*|number}
 */
UKeyApi.prototype.writeFile = function (fileName, data) {
  var hDev = this.connectDev();
  if (hDev == null) {
    return -1;
  }
  var rv = skf.SKF_WriteFile(hDev, fileName, data, 0, 255, 255);
  return rv.status;
};

/**
 * 读取文件
 * @param fileName 文件名称
 * @returns {string|SKF.SKF_ReadFile._xhr.statusText|SKF.SKF_ReadFile._xhr.statusText|*}
 */
UKeyApi.prototype.readFile = function (fileName) {
  var hDev = this.connectDev();
  if (hDev == null) {
    return "";
  }
  var rv = skf.SKF_ReadFile(hDev, fileName, 0);
  if (rv.status === 0) {
    return rv.value;
  }
  return "";
};

/*//枚举文件
function enumFiles() {
    var hDev = document.getElementById("t_connectdev").value;
    var rv = skf.SKF_EnumFiles(hDev);
    if (rv.status != 0) {
        document.getElementById("s_enumfiles").innerText = JSON.stringify(rv);
    } else {
        document.getElementById("t_enumfiles_filename").value = rv.value;
        document.getElementById("s_enumfiles").innerText = rv.status;
    }
}*/

/*//删除文件
function delFile() {
    var rv = skf.SKF_DeleteFile(document.getElementById("t_connectdev").value,
        document.getElementById("t_deleFile_name").value
    );
    document.getElementById("s_delFile").innerText = rv.status;
}*/

//枚举容器
UKeyApi.prototype.enumContainerName = function () {
  var hDev = this.connectDev();
  if (hDev == null) {
    return "-1";
  }
  return skf.SKF_EnumContainer(hDev);
};

//创建容器
UKeyApi.prototype.createContainerName = function (containerName) {
  var hDev = this.connectDev();
  if (hDev == null) {
    return "-1";
  }
  var rv = skf.SKF_CreateContainer(hDev, containerName);
  return rv.status;
};

/*//删除容器
function deleteContainerName() {
    var rv = skf.SKF_DeleteContainer(document.getElementById("t_connectdev").value, document.getElementById("t_containerName_delete").value);
    document.getElementById("s_containerName_delete").innerText = rv.status;
}*/

/**
 * 获取容器类型
 * @param containerName 容器名称
 * @returns {string|number|*} 1:RSA 2:ECC 0-尚未确定
 */
UKeyApi.prototype.getContainerType = function (containerName) {
  var hDev = this.connectDev();
  if (hDev == null) {
    return "-1";
  }
  var rv = skf.SKF_GetContainerType(hDev, containerName);
  if (rv.status === 0) {
    return rv.value;
  }
  return -1;
};

/*//导入证书
function importCertificate() {
    var rv = skf.SKF_ImportCertificate(document.getElementById("t_connectdev").value, document.getElementById("t_certdata").value, document.getElementById("t_certtype").value, document.getElementById("t_containerName").value);
    document.getElementById("s_importCertificate").innerText = rv.status;
}*/

/**
 * 导出证书
 * @param containerName 容器名称
 * @param certType 证书类型 0加密证书 1签名证书
 * @returns {string|SKF.SKF_ExportCertificate._xhr.statusText|SKF.SKF_ExportCertificate._xhr.statusText|*}
 */
UKeyApi.prototype.exportCertificate = function (containerName, certType) {
  var hDev = this.connectDev();
  if (hDev == null) {
    return "";
  }
  var rv = skf.SKF_ExportCertificate(hDev, containerName, certType);
  if (rv.status === 0) {
    return rv.value;
  }
  return "";
};

/**
 * 生成随机数
 * @param randomLen 随机数长度，大于 0
 * @returns {string|SKF.SKF_GenRandom._xhr.statusText|SKF.SKF_GenRandom._xhr.statusText|*}
 */
UKeyApi.prototype.genRandom = function (randomLen) {
  var hDev = this.connectDev();
  if (hDev == null) {
    return "";
  }
  var rv = skf.SKF_GenRandom(hDev, randomLen);
  if (rv.status === 0) {
    return rv.value;
  }
  return "";
};

/*//生成RSA签名密钥对
function genRSAKeyPair() {
    var rv = skf.SKF_GenRSAKeyPair(document.getElementById("t_connectdev").value, document.getElementById("t_containerName_genRSAKeyPair").value, document.getElementById("t_RSAKeyLen").value);
    document.getElementById("t_RSA_result").value = rv.value;
    document.getElementById("s_genRSAKeyPair").innerText = rv.status;
}*/

/*//生成RSA签名密钥对(异步)
function genRSAKeyPairAsync() {

    skf.SKF_GenRSAKeyPairAsync(document.getElementById("t_connectdev").value,
        document.getElementById("t_containerName_genRSAKeyPairAsync").value,
        document.getElementById("t_RSAKeyLenAsync").value,
        function (rv) {
            document.getElementById("t_RSA_resultAsync").value = rv.value;
            document.getElementById("s_genRSAKeyPairAsync").innerText = rv.status;
        });
}*/

/*//生成外部RSA密钥对
function genExtRSAKey() {
    var rv = skf.SKF_GenExtRSAKey(
        document.getElementById("t_connectdev").value,
        document.getElementById("t_genextrsa_keylength").value
    );
    document.getElementById("s_genextrsa").innerText = rv.status;
    document.getElementById("t_genextrsa_pubkey").value = rv.value.pubKey;
    document.getElementById("t_genextrsa_prikey").value = rv.value.priKey;
}*/

/**
 * 产生 ECC 密钥对，输出 Base64 编码的公钥
 * @param containerName 容器名称
 * @returns {string|SKF.SKF_GenECCKeyPair._xhr.statusText|SKF.SKF_GenECCKeyPair._xhr.statusText|*}
 */
UKeyApi.prototype.genECCKeyPair = function (containerName) {
  var hDev = this.connectDev();
  if (hDev == null) {
    return "";
  }
  var rv = skf.SKF_GenECCKeyPair(hDev, containerName);
  if (rv.status === 0) {
    return rv.value;
  }
  return "";
};
/*
//生成RSA加密密钥对
function genRSACipherKeyPair() {
    var rv = skf.SKF_GenRSACipherKeyPair(
        document.getElementById("t_connectdev").value,
        document.getElementById("t_genrsacipher_algtype").value,
        document.getElementById("t_genrsacipher_keylength").value,
        document.getElementById("t_genrsacipher_signpubkey").value,
        document.getElementById("t_genrsacipher_encpubkey").value,
        document.getElementById("t_genrsacipher_encprvkey").value
    );
    document.getElementById("s_genrsacipher").innerText = rv.status;
    document.getElementById("t_genrsacipher_wrappedKey").value = rv.value.wrappedKey;
    document.getElementById("t_genrsacipher_encryptedData").value = rv.value.encryptedData;
}*/

/*//生成ECC加密密钥对
function genECCCipherKeyPair() {
    var rv = skf.SKF_GenECCCipherKeyPair(
        document.getElementById("t_connectdev").value,
        document.getElementById("t_genecccipher_algtype").value,
        document.getElementById("t_genecccipher_keylength").value,
        document.getElementById("t_genecccipher_signpubkey").value,
        document.getElementById("t_genecccipher_encpubkey").value,
        document.getElementById("t_genecccipher_encprvkey").value
    );
    document.getElementById("s_genecccipher").innerText = rv.status;
    document.getElementById("t_genecccipher_value").value = rv.value;
}*/

/*//导入RSA加密密钥对
function importRSAKeyPair() {
    var rv = skf.SKF_ImportRSAKeyPair(document.getElementById("t_connectdev").value, document.getElementById("t_algType_importRSA").value, document.getElementById("t_wrappedKey_importRSA").value, document.getElementById("t_keyPairs_importRSA").value, document.getElementById("t_containerName_importRSA").value);
    document.getElementById("s_importRSAKeyPair").innerText = rv.status;
}*/

/*//RSA签名
function RSASignData() {
    var rv = skf.SKF_RSASignData(document.getElementById("t_connectdev").value, document.getElementById("t_containerName_RSASignData").value, document.getElementById("t_data_RSASignData").value);
    document.getElementById("t_RSASignData_result").value = rv.value;
    document.getElementById("s_RSASignData").innerText = rv.status;
}*/
/*//RSA签名
function ExtRSASignData() {
    var rv = skf.SKF_ExtRSAPriKeyOperation(document.getElementById("t_connectdev").value,
        document.getElementById("t_extRsaSign_prv").value,
        document.getElementById("t_data_extRSASignData").value);
    document.getElementById("t_extRSASignData_result").value = rv.value;
    document.getElementById("s_extRSASignData").innerText = rv.status;
}*/

/*//RSA验证签名
function RSAVerify() {
    var rv = skf.SKF_RSAVerify(document.getElementById("t_connectdev").value, document.getElementById("t_pubKey_RSAVerify").value, document.getElementById("t_data_RSAVerify").value, document.getElementById("t_signData_RSAVerify").value);
    document.getElementById("s_RSAVerify").innerText = rv.status;
}*/

/**
 * 导入 ECC 加密密钥对
 * @param containerName 容器名称
 * @param eccKeyPair  Base64 编码的加密密钥对
 * @returns {SKF.SKF_ImportECCKeyPair._xhr.status|SKF.SKF_ImportECCKeyPair._xhr.status|*|number}
 */
UKeyApi.prototype.importEccKeyPair = function (containerName, eccKeyPair) {
  var hDev = this.connectDev();
  if (hDev == null) {
    return -1;
  }
  var rv = skf.SKF_ImportECCKeyPair(hDev, containerName, eccKeyPair);
  return rv.status;
};

/**
 * 内部ECC签名
 * @param containerName 容器名称
 * @param data Base64 编码的被签名数据
 * @returns {string} Base64 编码格式签名值
 */
UKeyApi.prototype.eccSignData = function (containerName, data) {
  var hDev = this.connectDev();
  if (hDev == null) {
    return "";
  }
  var rv = skf.SKF_ECCSignData(hDev, containerName, data);
  if (rv.status === 0) {
    return rv.value;
  }
  return "";
};

/*//内部ECC签名
function exteccSignData() {
    var rv = skf.SKF_ExtECCSign(document.getElementById("t_connectdev").value,
        document.getElementById("t_ext_eccprvkey").value,
        document.getElementById("t_data_extEccSignData").value);
    document.getElementById("t_signdata_extEccSignData").value = rv.value;
    document.getElementById("s_exteccSignData").innerText = rv.status;
}*/

/**
 * ECC 外来公钥验签
 * @param pubKey Base64 编码的公钥
 * @param data Base64 编码的被签名数据 HASH
 * @param signData Base64 编码的签名结果
 * @returns {SKF.SKF_ECCVerify._xhr.status|SKF.SKF_ECCVerify._xhr.status|*|number}
 */
UKeyApi.prototype.eccVerify = function (pubKey, data, signData) {
  var hDev = this.connectDev();
  if (hDev == null) {
    return -1;
  }
  var rv = skf.SKF_ECCVerify(hDev, pubKey, data, signData);
  return rv.status;
};

/*//RSA公钥加密
function RSAPubEncrypt() {
    var rv = skf.SKF_RSAPubEncrypt(document.getElementById("t_connectdev").value, document.getElementById("t_pubkey_RSAPubEncrypt").value, document.getElementById("t_data_RSAPubEncrypt").value, document.getElementById("t_filetype_RSAPubEncrypt").value);
    document.getElementById("t_encData_RSAPubEncrypt").value = rv.value;
    document.getElementById("s_RSAPubEncrypt").innerText = rv.status;
}*/

/*//RSA私钥解密
function RSAPrvDecrypt() {
    var rv = skf.SKF_RSAPrvDecrypt(document.getElementById("t_connectdev").value, document.getElementById("t_prvkey_RSAPrvDecrypt").value, document.getElementById("t_encData_RSAPrvDecrypt").value, document.getElementById("t_filetype_RSAPrvDecrypt").value);
    document.getElementById("t_decData_RSAPrvDecrypt").value = rv.value;
    document.getElementById("s_RSAPrvDecrypt").innerText = rv.status;
}*/

/**
 * ECC公钥加密
 * @param pubkey Base64 编码的公钥
 * @param data 明文数据
 * @param dataIsBase64 明文数据是否 Base64 编码格式 0 非base64 ; 1 base64
 * @returns {string|SKF.SKF_ECCPubEncrypt._xhr.statusText|SKF.SKF_ECCPubEncrypt._xhr.statusText|*}
 * @constructor
 */
UKeyApi.prototype.ECCPubEncrypt = function (pubkey, data, dataIsBase64) {
  var hDev = this.connectDev();
  if (hDev == null) {
    return "";
  }
  var rv = skf.SKF_ECCPubEncrypt(hDev, pubkey, data, dataIsBase64);
  if (rv.status === 0) {
    return rv.value;
  }
  return "";
};

/**
 * ECC私钥解密
 * @param prvKey Base64 编码的私钥
 * @param encData Base64 编码的密文数据
 * @param dataIsBase64 输出明文数据是否 Base64 编码格式 0 非 1是
 * @returns {string|SKF.SKF_ECCPrvDecrypt._xhr.statusText|SKF.SKF_ECCPrvDecrypt._xhr.statusText|*}
 * @constructor
 */
UKeyApi.prototype.ECCPrvDecrypt = function (prvKey, encData, dataIsBase64) {
  var hDev = this.connectDev();
  if (hDev == null) {
    return "";
  }
  var rv = skf.SKF_ECCPrvDecrypt(hDev, prvKey, encData, dataIsBase64);
  if (rv.status === 0) {
    return rv.value;
  }
  return "";
};

/**
 * 导出公钥
 * @param containerName 容器名称
 * @param signFlag 1签名公钥 2加密公钥
 * @param pubKeyFormat 公钥数据结构 0 GM/T0016结构体  1-RSA(bitlen|Modulus-256|PublicExponent-4 输出公钥), ECC(bitlen|32X|32Y 输出公钥。
 * @returns {string|SKF.SKF_ExportPublicKey._xhr.statusText|SKF.SKF_ExportPublicKey._xhr.statusText|*}
 */
UKeyApi.prototype.exportPublicKey = function (
  containerName,
  signFlag,
  pubKeyFormat,
) {
  var hDev = this.connectDev();
  if (hDev == null) {
    return "";
  }
  var rv = skf.SKF_ExportPublicKey(hDev, containerName, signFlag, pubKeyFormat);
  if (rv.status === 0) {
    return rv.value;
  }
  return "";
};

/**
 * 杂凑初始化
 */
UKeyApi.prototype.digestInit = function (hDev, pubKey) {
  var rv = skf.SKF_DigestInit(hDev, 1, pubKey, "1234567812345678", 0);
  if (rv.status === 0) {
    return rv.value;
  }
  return "";
};
/**
 *
 */
UKeyApi.prototype.digest = function (hHash, data) {
  var rv = skf.SKF_Digest(hHash, data, 0);
  if (rv.status === 0) {
    return rv.value;
  }
  return "";
};
/**
 * 关闭杂凑
 */
UKeyApi.prototype.closeDigestHandle = function (hHash) {
  skf.SKF_CloseDigestHandle(hHash);
};

/*//杂凑初始化
function digestInit() {
    var rv = skf.SKF_DigestInit(document.getElementById("t_connectdev").value,
        document.getElementById("t_algType_digestInit").value,
        document.getElementById("t_pubKey_digestInit").value,
        document.getElementById("t_pucId_digestInit").value,
        document.getElementById("t_pucIdisBase64").value);
    document.getElementById("t_data_digestInit").value = rv.value;
    document.getElementById("s_digestInit").innerText = rv.status;
}*/

/*
//单组数据杂凑
function digest() {
    var rv = skf.SKF_Digest(document.getElementById("t_data_digestInit").value,
        document.getElementById("t_data_digest").value,
        document.getElementById("t_dataFormat").value);
    document.getElementById("t_result_digest").value = rv.value;
    document.getElementById("s_digest").innerText = rv.status;
}
*/

/*//多组数据杂凑
function digestUpdate() {
    var rv = skf.SKF_DigestUpdate(document.getElementById("t_data_digestInit").value,
        document.getElementById("t_data_digestUpdate").value,
        document.getElementById("t_dataformat_digestUpdate").value);
    document.getElementById("s_digestUpdate").innerText = rv.status;
}*/

/*//结束多组数据杂凑
function digestFinal() {
    var rv = skf.SKF_DigestFinal(document.getElementById("t_data_digestInit").value);
    document.getElementById("t_result_digestFinal").value = rv.value;
    document.getElementById("s_digestFinal").innerText = rv.status;
}*/

/*//关闭杂凑对象
function closeDigestHandle() {
    var rv = skf.SKF_CloseDigestHandle(document.getElementById("t_data_closedigesthandle").value);
    document.getElementById("s_closedigesthandle").innerText = rv.status;
}*/

/*//导入明文会话密钥
function setSymmKey() {
    var rv = skf.SKF_SetSymmKey(document.getElementById("t_connectdev").value, document.getElementById("t_algType_setSymmKey").value, document.getElementById("t_symmKey_setSymmKey").value);
    document.getElementById("t_result_setSymmKey").value = rv.value;
    document.getElementById("s_setSymmKey").innerText = rv.status;
}*/

/*//导入会话密钥
function importSymmKey() {
    var rv = skf.SKF_ImportSymmKey(document.getElementById("t_connectdev").value, document.getElementById("t_containerName_importSymmKey").value, document.getElementById("t_encSymmKey_importSymmKey").value, document.getElementById("t_algType_importSymmKey").value);
    document.getElementById("t_result_importSymmKey").value = rv.value;
    document.getElementById("s_importSymmKey").innerText = rv.status;
}*/

/*//关闭会话密钥句柄
function closeSymmKey() {
    var rv = skf.SKF_CloseSymmKey(document.getElementById("t_hSymmKey_closeSymmKey").value);
    document.getElementById("s_closeSymmKey").innerText = rv.status;
}*/

/*//加密初始化
function encryptInit() {
    var rv = skf.SKF_EncryptInit(document.getElementById("t_hSymmKey_encryptInit").value, document.getElementById("t_iv_encryptInit").value, document.getElementById("t_paddingType_encryptInit").value, document.getElementById("t_feedBitLen_encryptInit").value);
    document.getElementById("s_encryptInit").innerText = rv.status;
}*/

/*//单组数据加密
function encrypt() {
    var rv = skf.SKF_Encrypt(document.getElementById("t_hSymmKey_encrypt").value, document.getElementById("t_data_encrypt").value, document.getElementById("t_dataType_encrypt").value, document.getElementById("t_outDataType_encrypt").value);
    document.getElementById("t_result_encrypt").value = rv.value;
    document.getElementById("s_encrypt").innerText = rv.status;
}*/

/*//解密初始化
function decryptInit() {
    var rv = skf.SKF_DecryptInit(document.getElementById("t_hSymmKey_decryptInit").value, document.getElementById("t_iv_decryptInit").value, document.getElementById("t_paddingType_decryptInit").value, document.getElementById("t_feedBitLen_decryptInit").value);
    document.getElementById("s_decryptInit").innerText = rv.status;
}*/

/*//解密数据
function decrypt() {
    var rv = skf.SKF_Decrypt(document.getElementById("t_hSymmKey_decrypt").value,
        document.getElementById("t_data_decrypt").value,
        document.getElementById("t_encDataType_decrypt").value,
        document.getElementById("t_outDataType_decrypt").value);
    document.getElementById("t_result_decrypt").value = rv.value;
    document.getElementById("s_decrypt").innerText = rv.status;
}*/

/*//枚举应用
function enumApplication() {
    var rv = skf.SKF_EnumApplication(document.getElementById("t_connectdev").value);
    document.getElementById("t_result_enumApplication").value = rv.value;
    document.getElementById("s_enumApplication").innerText = rv.status;
}*/

/*//打开应用
function openApplication() {
    var rv = skf.SKF_OpenApplication(document.getElementById("t_connectdev").value, document.getElementById("t_appName_openApplication").value);
    document.getElementById("s_openApplication").innerText = rv.status;
}*/

/*//关闭钥匙
function disConnectDev() {

    document.getElementById("s_disconnectdev").innerText = skf.SKF_DisConnectDev(document.getElementById("t_connectdev").value).status;
}*/
/**
 * 获取pin信息
 * @param verifyPin PIN，最大长度为 16
 * @param pinType 0:管理员 PIN 1:用户 PIN
 * @returns {number} 0成功
 */
UKeyApi.prototype.getPinInfo = function (pinType) {
  var hDev = this.connectDev();
  if (hDev == null) {
    return -1;
  }
  var rv = skf.SKF_GetPINInfo(hDev, pinType);
  return rv.status;
};

export default UKeyApi;
