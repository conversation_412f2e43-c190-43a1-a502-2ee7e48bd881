import global from "@/constants/GlobalConstants";

function initUrl(url, query = {}) {
  let _url = url;
  for (let key of Object.keys(query)) {
    if (query.hasOwnProperty(key)) {
      _url = _url.replace(`:${key}`, query[key]);
    }
  }
  return `${window.location.origin}${window.location.pathname}${_url}`;
}

/**
 * portal交互包装utils
 *  暂不考虑独立系统时的交互问题
 */
const PortalBridge = function (routerInstance) {
  this.routerInstance = routerInstance;
  this.MSGTYPE = {
    INFO: "info",
  };
};

PortalBridge.prototype.getOriginUrl = function () {
  return window.sessionStorage.getItem("portalOrigin") || document.referrer;
};

/**
 * @description 使用portal的alert信息提示
 * @params message 提示信息
 * @params type 提示类型，使用内部的MSGTYPE枚举
 * @params duration 延迟时间
 */
PortalBridge.prototype.alert = function (message, type, duration) {
  window.top.postMessage(
    {
      type: "alert",
      alert: {
        type: type || this.MSGTYPE.INFO,
        duration: duration || 1000,
        message: message || "",
      },
    },
    this.getOriginUrl(),
  );
};

/**
 * 在新tab中跳转页面  ?pifp_target=aloneIframe
 */
PortalBridge.prototype.openNewTab = function (target, data) {
  const routeData = this.routerInstance.resolve({
    name: target,
    params: data,
    query: Object.assign(
      { routeMod: "outer", pifp_target: "aloneIframe" },
      data,
    ),
  });

  if (global.isDev) {
    // 如果是本地环境，则需要在新窗口打开
    window.open(routeData.href, "_blank");
    return;
  }

  if (global.isPlat) return;
  window.top.postMessage(
    {
      type: "openNewIFrame",
      path: initUrl(routeData.href),
      label: routeData.route.meta.title,
      name: routeData.route.name,
      title: routeData.route.meta.title,
    },
    this.getOriginUrl(),
  );
};

/**
 * 打开统一web平台其他模块菜单路由页面
 * @param {*} target
 * @param {*} data
 */
PortalBridge.prototype.openPortalMenu = function (target, data) {
  window.top.postMessage(
    {
      type: "openMenuItemByRouteName",
      menuRoute: data.menuRoute, //约定好的，在上级平台时的菜单路由名称
      query: data.query,
    },
    this.getOriginUrl(),
  );
};

PortalBridge.prototype.closeCurrentTag = function () {
  if (global.isDev) {
    window.close();
    return;
  }
  if (global.isPlat) return;
  window.parent.postMessage(
    {
      type: "closeCurrentTag",
    },
    this.getOriginUrl(),
  );
};

PortalBridge.prototype.closeTag = function (path = document.location.href) {
  const toCloseRoute = {
    type: "closeTag",
    path: path,
  };
  window.parent.postMessage(toCloseRoute, this.getOriginUrl());
};

PortalBridge.prototype.dialog = function (value) {
  window.top.postMessage(
    {
      type: "dialog",
      dialogValue: value,
    },
    this.getOriginUrl(),
  );
};
PortalBridge.prototype.initData = function (params) {
  window.top.postMessage(
    {
      type: "initData",
      initValue: params,
    },
    this.getOriginUrl(),
  );
};

// License授权成功，触发给父组件
PortalBridge.prototype.LicenceSucc = function () {
  if (global.isPlat) return;
  window.top.postMessage(
    {
      type: "LicenceSucc",
    },
    this.getOriginUrl(),
  );
};

// 退出登录
PortalBridge.prototype.reLogin = function (type) {
  if (global.isDev || global.isPlat) {
    // 如果是本地环境，则不执行该操作
    return;
  }
  window.localStorage.removeItem("swWarnAuthCode");
  window.localStorage.removeItem("swShowGoContact");
  window.localStorage.removeItem("webShellObj");
  window.top.postMessage(
    {
      type: "reLogin",
      reLoginType: type,
    },
    this.getOriginUrl(),
  );
};

export default PortalBridge;
