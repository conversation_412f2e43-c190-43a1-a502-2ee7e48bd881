import { sm2 } from "sm-crypto";
import { SM2 } from "gm-crypto";

//为0时在加密的串前加上04,后端才能解密成功,同样后端加密后也会返回带04的加密串给前端,cipherMode为1的话必须去掉04才能解密成功
const cipherMode = 1; // 选择加密策略，1 - C1C3C2，0 - C1C2C3，默认为1
let keypair = sm2.generateKeyPairHex();
// let publicKey = keypair.publicKey; // 公钥
// let privateKey = keypair.privateKey; // 私钥
let publicKey =
  "0477d987bb38e0ba805a3c89d8c85531a5e953bf5e3e6d118bdda176e22370930741dad7d38ecf225b548dde59f8b8f2a761951240904129dda4b921f0f561bbbb";
// 默认生成公钥 130 位太长，可以压缩公钥到 66 位
// const compressedPublicKey = sm2.compressPublicKeyHex(publicKey); // compressedPublicKey 和 publicKey 等价
// sm2.comparePublicKeyHex(publicKey, compressedPublicKey); // 判断公钥是否等价

export const sm = {
  encodeSM2(txt) {
    let encryptData = sm2.doEncrypt(txt, publicKey, cipherMode);
    return encryptData;
  },
  decodeSM2(data) {
    //   let dataHex = data.substring(2).toLocaleLowerCase();
    return sm2.doDecrypt(data, privateKey, cipherMode);
  },
};
export const gm = {
  encodeSM2(txt) {
    let encryptData = SM2.encrypt(txt, publicKey, {
      inputEncoding: "utf8",
      outputEncoding: "base64",
    });
    return encryptData;
  },
  decodeSM2(data) {
    let decryptData = SM2.decrypt(data, privateKey, {
      inputEncoding: "base64",
      outputEncoding: "utf8",
    });
    return decryptData;
  },
};
