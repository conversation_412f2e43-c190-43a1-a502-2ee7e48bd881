import axios from "axios";
import { MessageBox, Loading } from "element-ui";
import store from "@/store";
import { getToken, getTenantCode } from "@/utils/auth";
import errorCode from "@/utils/errorCode";
import {
  tansParams,
  blobValidate,
  downloadTansParams,
  getBasePrefix,
} from "@/utils/util";
import cache from "@/plugins/cache";
import { saveAs } from "file-saver";
import { isZh } from "@/utils/language";
import i18n from "@/i18n";
import { getSession, setSession } from "./session";
import swKey from "./uKey/swKey";
let $ukey = new swKey();
import { cancelPending } from "./cancelRequest";
import { errorMsg } from "./notice";
import JSONBIG from "json-bigint";

let downloadLoadingInstance;
// 是否显示重新登录
export let isRelogin = { show: false };

axios.defaults.headers["Content-Type"] = "application/json;charset=utf-8";
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: process.env.VUE_APP_BASE_API,
  // 超时
  timeout: 40000,
});

service.defaults.transformResponse = [
  function (data, headers) {
    if (typeof data === "string") {
      try {
        const json = JSONBIG({
          storeAsString: true,
        });
        const result = json.parse(data);
        return result;
      } catch (e) {
        /* Ignore */
      }
    }
    return data;
  },
];
// request拦截器
service.interceptors.request.use(
  (config) => {
    // IE中图片变成了缓存
    config.headers["If-Modified-Since"] = "0";
    config.headers["Cache-Control"] = "no-cache";
    // 是否需要设置 token
    const isToken = (config.headers || {}).isToken === false;
    const isTenantCode = (config.headers || {}).isTenantCode === false;
    // 是否需要防止数据重复提交
    const isRepeatSubmit = (config.headers || {}).repeatSubmit === false;
    if (getToken() && !isToken) {
      config.headers["AiPortalToken"] = getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    if (getTenantCode() && !isTenantCode) {
      config.headers["SecTenantCode"] = getTenantCode();
    }

    config.headers["lang"] = isZh() ? "zh_CN" : "en_US";

    // get请求映射params参数
    if (config.method === "get" && config.params) {
      let url = config.url + "?" + tansParams(config.params);
      url = url.slice(0, -1);
      config.params = {};
      config.url = url;
    }
    if (!isRepeatSubmit && config.method === "post") {
      const requestObj = {
        url: config.url,
        data:
          typeof config.data === "object"
            ? JSON.stringify(config.data)
            : config.data,
        time: new Date().getTime(),
      };
      const sessionObj = cache.session.getJSON("sessionObj");
      if (
        sessionObj === undefined ||
        sessionObj === null ||
        sessionObj === ""
      ) {
        cache.session.setJSON("sessionObj", requestObj);
      } else {
        const s_url = sessionObj.url; // 请求地址
        const s_data = sessionObj.data; // 请求数据
        const s_time = sessionObj.time; // 请求时间
        const interval = 500; // 间隔时间(ms)，小于此时间视为重复提交
        if (
          s_data === requestObj.data &&
          requestObj.time - s_time < interval &&
          s_url === requestObj.url
        ) {
          const message = i18n.t("common.requestPending");
          console.warn(`[${s_url}]: ` + message);
          // return Promise.reject(new Error(message));
        } else {
          cache.session.setJSON("sessionObj", requestObj);
        }
      }
    }
    // 判断是否开启ukey拔插校验
    if (getSession("UKeyInsertStatus") && getSession("UKeyInsertStatus") == 1) {
      if (!isRelogin.show) {
        setSession("isUkReLoginDig", 0);
      }
      // 若是uk方式登录，需要拦截看是否插入这uk，是否更换了uk
      if (
        !isRelogin.show &&
        getSession("isUkLogin") &&
        config.url != "/login/logOut" &&
        getSession("isUkReLoginDig") != 1
      ) {
        if (
          (getSession("isUkNoLogin") && getSession("isAddUk")) ||
          (getSession("isUkNoLogin") && !getSession("isAddUk"))
        ) {
          return config;
        } else if (!getSession("isUkNoLogin") && getSession("isAddUk")) {
          // 如果新增过uk用户,直接退出登录
          confirmDig(i18n.t("common.updateUk"), config);
          return config;
        }
        let msg = "";
        $ukey.checkPlugin((version) => {
          if (!version) {
            msg = i18n.t("common.ukControl");
            confirmDig(msg, config);
          } else {
            if (!$ukey.isUKeyExist()) {
              msg = i18n.t("common.unInsetUk");
              confirmDig(msg, config);
            }
            if (!isRelogin.show) {
              let sign = $ukey.signData("sdnfmsfmsfsnfnsfheheehheh");
              if (!sign) {
                msg = i18n.t("common.updateUk");
                confirmDig(msg, config);
              }
            }
          }
        });
      }
    }

    return config;
  },
  (error) => {
    Promise.reject(error);
  },
);

// 响应拦截器
service.interceptors.response.use(
  (res) => {
    // 未设置状态码则默认成功状态
    const code = res.data.code || "0";
    // 获取错误信息
    const msg =
      errorCode[code] ||
      res.data.message ||
      res.data.msg ||
      errorCode["default"];
    // 二进制数据则直接返回
    if (
      res.request.responseType === "blob" ||
      res.request.responseType === "arraybuffer"
    ) {
      return res;
    }
    if (code === "00888888" || code === "88888" || code == "00060000") {
      if (swGlobal.isInIframe == "true") {
        window.top.postMessage(
          {
            type: "reLogin",
          },
          "*",
        );
      } else if (!isRelogin.show) {
        isRelogin.show = true;
        MessageBox.confirm(
          i18n.t("common.reLoginTip"),
          i18n.t("common.sysTip"),
          {
            confirmButtonText: i18n.t("common.reLogin"),
            // cancelButtonText: i18n.t("common.cancel"),
            showCancelButton: false,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            showClose: false,
            type: "warning",
          },
        )
          .then(() => {
            isRelogin.show = false;
            store.dispatch("LogOut").then(() => {
              location.href = `${getBasePrefix()}login`;
            });
          })
          .catch(() => {
            isRelogin.show = false;
          });
      }
      return Promise.reject(i18n.t("common.fail"));
    } else if (code === 500) {
      errorMsg(msg);
      return Promise.reject(new Error(msg));
    } else if (code !== "0" && code !== "00000") {
      errorMsg(msg);
      return Promise.reject(res.data);
    } else {
      return {
        isSuccess: res.data.code == "0" || res.data.code == "00000",
        ...res.data,
        data: res.data?.result ?? res.data.data,
      };
    }
  },
  (error) => {
    console.log("err" + error);
    let { message, config } = error;
    if (message == "Network Error") {
      message = i18n.t("common.backEndTip");
    } else if (message.includes("timeout")) {
      message = i18n.t("common.sysInterTip");
      // 编辑网卡、绑定网卡、解绑网卡、设为管理网口超时报：配置已更新, 请尝试修改ip地址
      if (
        (config && config.url == "/network/card/update") ||
        config.url == "/network/card/setManage" ||
        config.url == "/network/card/bond/delete" ||
        config.url == "/network/card/bond/add"
      ) {
        message = i18n.t("common.setNetworkCardUpdateTip");
      }
    } else if (message.includes("Request failed with status code")) {
      let content =
        i18n.t("common.interFace") +
        message.substr(message.length - 3) +
        i18n.t("common.abnormal");
      if (
        message.includes("Request failed with status code 502") &&
        getSession("isRecovery")
      ) {
        content = i18n.t("common.restartTip");
      }
      message = content;
    } else if (message.includes("errorUk")) {
      message = i18n.t("common.errorUk");
    }
    errorMsg(message);
    return Promise.reject(error);
  },
);

// 弹窗
export function confirmDig(msg, config) {
  if (!isRelogin.show) {
    isRelogin.show = true;
    setSession("isUkReLoginDig", 1);
    MessageBox.confirm(msg, i18n.t("common.sysTip"), {
      confirmButtonText: i18n.t("common.reLogin"),
      showClose: false,
      showCancelButton: false,
      closeOnClickModal: false,
      closeOnPressEscape: false,
      type: "warning",
    })
      .then(() => {
        // isRelogin.show = false
        if (getToken()) {
          store.dispatch("LogOut").then(() => {
            location.href = `${getBasePrefix()}login`;
          });
        } else {
          store.dispatch("FedLogOut").then(() => {
            location.href = `${getBasePrefix()}login`;
          });
        }
      })
      .catch(() => {
        // isRelogin.show = false
        // setSession('isUkReLoginDig', 0)
      });

    cancelPending(config);
    return Promise.reject(new Error("errorUk"));
  }
}

export function downloadJsonData(url, params, name) {
  downloadLoadingInstance = Loading.service({
    text: i18n.t("common.downLoading"),
    spinner: "el-icon-loading",
    background: "rgba(0, 0, 0, 0.7)",
  });
  return service
    .post(url, params, {
      responseType: "blob",
    })
    .then(async (res) => {
      const isLogin = await blobValidate(res.data);
      if (isLogin) {
        const blob = new Blob([res.data]);
        let headerName = res.headers["content-disposition"].match(
          /filename\*?=['"]?([^"';]*)['"]?/,
        );
        let fileName = "";
        if (headerName) {
          fileName = decodeURIComponent(headerName[1] || "");
        }
        saveAs(blob, fileName || name);
      } else {
        const resText = await res.data.text();
        const rspObj = JSON.parse(resText);
        const errMsg =
          errorCode[rspObj.code] ||
          rspObj.message ||
          rspObj.msg ||
          errorCode["default"];
        errorMsg(errMsg);
      }
      downloadLoadingInstance.close();
    })
    .catch((r) => {
      console.error(r);
      errorMsg(i18n.t("common.downloadError"));
      downloadLoadingInstance.close();
    });
}
// 通用下载方法
export function download(url, params, filename) {
  downloadLoadingInstance = Loading.service({
    text: i18n.t("common.downLoading"),
    spinner: "el-icon-loading",
    background: "rgba(0, 0, 0, 0.7)",
  });
  return service
    .post(url, params, {
      transformRequest: [
        (params) => {
          return tansParams(params);
        },
      ],
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      responseType: "blob",
    })
    .then(async (res) => {
      const isLogin = await blobValidate(res.data);
      if (isLogin) {
        const blob = new Blob([res.data]);
        saveAs(blob, filename);
      } else {
        const resText = await res.data.text();
        const rspObj = JSON.parse(resText);
        const errMsg =
          errorCode[rspObj.code] ||
          rspObj.message ||
          rspObj.msg ||
          errorCode["default"];
        errorMsg(errMsg);
      }
      downloadLoadingInstance.close();
    })
    .catch((r) => {
      console.error(r);
      errorMsg(i18n.t("common.downloadError"));
      downloadLoadingInstance.close();
    });
}

export function downLoadFile(url, params, name, timeout = 40000) {
  downloadLoadingInstance = Loading.service({
    text: i18n.t("common.downLoading"),
    spinner: "el-icon-loading",
    background: "rgba(0, 0, 0, 0.7)",
  });
  let data = params ? "?" + downloadTansParams(params) : "";
  url = url + data;
  return service
    .get(url, {
      responseType: "blob",
      timeout,
    })
    .then(async (res) => {
      const isLogin = await blobValidate(res.data);
      if (isLogin) {
        const blob = new Blob([res.data]);
        let headerName = res.headers["content-disposition"].match(
          /(?:.*filename\*|filename)=(?:([^'"]*)''|("))([^;]+)\2(?:[;`\n]|$)/,
        );
        let fileName = "";
        if (headerName) {
          fileName = decodeURIComponent(headerName[3] || "");
        }
        saveAs(blob, fileName || name);
      } else {
        const resText = await res.data.text();
        const rspObj = JSON.parse(resText);
        const errMsg =
          errorCode[rspObj.code] ||
          rspObj.message ||
          rspObj.msg ||
          errorCode["default"];
        errorMsg(errMsg);
      }
      downloadLoadingInstance.close();
    })
    .catch((r) => {
      errorMsg(i18n.t("common.downloadError"));
      downloadLoadingInstance.close();
    });
}

export default service;
