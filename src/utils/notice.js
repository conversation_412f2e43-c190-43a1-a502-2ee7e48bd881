import { Message, MessageBox } from "element-ui";

const duration = 2 * 1000;

export function warningMsg(mes) {
  Message({
    message: mes,
    type: "warning",
    duration: duration,
    showClose: true,
    customClass: "zZindex",
  });
}
export function successMsg(mes) {
  Message({
    message: mes,
    type: "success",
    duration: duration,
    showClose: true,
    customClass: "zZindex",
  });
}
export function infoMsg(mes) {
  Message({
    message: mes,
    type: "info",
    duration: duration,
    showClose: true,
    customClass: "zZindex",
  });
}
export function errorMsg(mes) {
  Message({
    message: mes,
    type: "error",
    duration: duration,
    showClose: true,
    customClass: "zZindex",
  });
}

export function alertMessage(mes, type = "warning") {
  let title =
    type === "error" ? this.$t("common.error") : this.$t("common.tips");
  MessageBox.alert(mes, title, {
    customClass: "alertClass",
    confirmButtonText: this.$t("common.determine"),
    type,
  });
}
export function alertHtmlMessage(
  msg,
  tip = this.$t("common.tips"),
  type = "warning",
) {
  return MessageBox.alert(msg, tip, {
    customClass: "alertHtmlClass",
    dangerouslyUseHTMLString: true,
    type,
  });
}

export function confirmMessage(
  mes,
  type = "warning",
  showCancelButton = true,
  closeOnClickModal = true,
  showClose = true,
  confirmButtonText = this.$t("common.determine"),
  tips = this.$t("common.tips"),
  closeOnPressEscape = true,
  cancelButtonText = this.$t("common.cancel"),
  dangerouslyUseHTMLString = false,
) {
  return MessageBox.confirm(mes, tips, {
    customClass: "confirmMsgClass",
    confirmButtonText: confirmButtonText,
    cancelButtonText: cancelButtonText,
    cancelButtonClass: "cancel-btn",
    showCancelButton: showCancelButton,
    closeOnClickModal: closeOnClickModal,
    closeOnPressEscape: closeOnPressEscape,
    showClose: showClose,
    dangerouslyUseHTMLString,
    type,
  });
}

export function promptMessage(
  mes,
  tips = this.$t("common.tips"),
  opts,
  showCancelButton = true,
  closeOnClickModal = true,
  confirmButtonText = this.$t("common.determine"),
  closeOnPressEscape = true,
  cancelButtonText = this.$t("common.cancel"),
) {
  return MessageBox.prompt(mes, tips, {
    customClass: "confirmMsgClass",
    confirmButtonText: confirmButtonText,
    cancelButtonText: cancelButtonText,
    cancelButtonClass: "cancel-btn",
    showCancelButton: showCancelButton,
    closeOnClickModal: closeOnClickModal,
    closeOnPressEscape: closeOnPressEscape,
    ...opts,
  });
}

export function tipErrorOnce() {
  var istip = false;
  function tip(mes) {
    if (istip == true) {
      return;
    }
    istip = true;
    Message({
      message: mes,
      type: "error",
      duration: duration,
      showClose: true,
      customClass: "zZindex",
      onClose: () => {
        istip = false;
      },
    });
  }
  return tip;
}
