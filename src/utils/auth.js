import Cookies from "js-cookie";

const TokenKey = "AiPortalToken";
const TenantCodeKey = "SecTenantCode";
const logoutURLKey = "ccspLoginUrl";

export function getToken() {
  return localStorage.getItem(TokenKey) || Cookies.get(TokenKey);
}

export function setToken(token, flag = false) {
  if (flag) {
    if (location.href.includes("https")) {
      let sameSite = "None";
      let secure = true;
      return Cookies.set(TokenKey, token, { secure, sameSite });
    } else {
      return localStorage.setItem(TokenKey, token);
    }
  } else {
    return Cookies.set(TokenKey, token);
  }
}

export function removeToken() {
  localStorage.removeItem(TokenKey);
  Cookies.remove(TokenKey);
}

export function getTenantCode() {
  return Cookies.get(TenantCodeKey);
}

export function setTenantCode(tenantCode) {
  return Cookies.set(TenantCodeKey, tenantCode);
}

export function removeTenantCode() {
  return Cookies.remove(TenantCodeKey);
}
export function getLogoutURL() {
  return Cookies.get(logoutURLKey);
}

export function setLogoutURL(logoutURL) {
  return Cookies.set(logoutURLKey, logoutURL);
}

export function removeLogoutURL() {
  return Cookies.remove(logoutURLKey);
}
