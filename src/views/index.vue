<template>
  <div class="home-box full-height">
    <!-- 首页<iframe -->
    <i-frame v-if="url" :src="url" style="height: 100%" />
  </div>
</template>

<script>
import iFrame from "@/components/iFrame/index";
import { getIndexMenu } from "@/api/menu";
export default {
  name: "Index",
  components: {
    iFrame,
  },
  data() {
    return {
      url: "",
      isShowPadding: true,
    };
  },
  mounted() {
    window.$eventBus.$on("LicenceSucc", this.LicenceSucc);
    this.getUrl();
  },
  methods: {
    getUrl() {
      getIndexMenu().then((res) => {
        this.url = window.location.origin + res.data;
      });
    },
    LicenceSucc() {
      location.reload();
    },
  },
};
</script>

<style scoped lang="scss">
.home-box {
  background: #fff;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
}

.no-padding {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
</style>
