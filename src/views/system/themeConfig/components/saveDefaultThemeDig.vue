<template>
  <div class="edit-config">
    <el-dialog
      :title="$t('themeConfig.setDefaultTheme')"
      :visible.sync="dialogVisible"
      width="700px"
      :before-close="onCloseClick"
      :close-on-click-modal="false"
      append-to-body
      @open="onOpenDig"
      class="edit-config-dialog"
    >
      <div class="config-content" v-loading="loading">
        <el-form
          @submit.native.prevent
          :model="configForm"
          label-position="right"
          :rules="rules"
          ref="configFormRef"
        >
          <!-- 主题-配置值  -->
          <el-form-item prop="itemConfigValue">
            <div class="setting-drawer-block-checbox">
              <sidebar-svg
                v-for="(item, i) in themeAll"
                :key="i"
                :currentThemeId="configForm.itemConfigValue"
                :themeData="item"
                @handleTheme="handleTheme"
              ></sidebar-svg>
              <el-input
                type="hidden"
                v-model="configForm.itemConfigValue"
              ></el-input>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="oper-btn cancel-btn"
          id="cancelBtnId"
          @click="onCloseClick"
          >{{ $t("common.cancel") }}</el-button
        >
        <el-button
          type="primary"
          class="oper-btn"
          id="submitBtnId"
          @click="onSubmitClick"
          :disabled="loading"
          >{{ $t("common.submit") }}</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { setDefaultTheme } from "@/api/theme";
// import SidebarSvg from "@/views/themeConfig/components/sidebarSvg.vue";
import SidebarSvg from "./sidebarSvg.vue";
import { formatColor_deep } from "@/utils/index"; // theme转换样式颜色

export default {
  name: "saveDefaultThemeDig",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    defaultThemeId: {
      type: String,
      default: "",
    },

    themeAll: {
      type: Array,
      default: () => [],
    },
  },
  components: { SidebarSvg },
  data() {
    return {
      configForm: {
        itemConfigValue: "",
      },
      rules: {
        itemConfigValue: [
          {
            required: true,
            message: this.$t("placeholder.require"),
            trigger: "blur",
          },
        ],
      },
      loading: false,
    };
  },
  created() {},
  watch: {
    themeAll: {
      handler(val) {
        if (val.length > 0) {
          this.initThemeAll();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    formatColor_deep,
    initThemeAll() {
      this.setLeftColor(this.themeAll);
    },
    setLeftColor(list) {
      list.forEach((obj, i) => {
        if (obj.sideTheme == "theme-theme") {
          obj.leftColorActive = this.formatColor_deep(obj.theme, 80);
          obj.leftColor = obj.theme;
        } else if (
          obj.sideTheme == "theme-dark" ||
          obj.sideTheme == "theme-light"
        ) {
          obj.leftColorActive = obj.theme;
          obj.leftColor = obj.sideTheme == "theme-dark" ? "#303648" : "#ffffff";
        } else if (obj.sideTheme == "theme-custom") {
          obj.leftColorActive = obj.subMenuBackgroundActiveCustom;
          obj.leftColor = obj.menuBackgroundCustom;
        } else {
          obj.leftColorActive = "#fff";
          obj.leftColor = "#fff";
        }
        obj.topColor = obj.topBackgroundCustom || "#fff";
      });
    },
    onCloseClick() {
      this.$refs.configFormRef.resetFields();
      this.$emit("eventClose", false);
    },
    onOpenDig() {
      this.$nextTick(() => {
        this.configForm.itemConfigValue = this.defaultThemeId;
      });
    },
    handleTheme(obj) {
      this.configForm.itemConfigValue = obj.themeId;
    },
    onSubmitClick() {
      this.$refs.configFormRef.validate((valid) => {
        if (valid) {
          this.loading = true;
          let params = {
            themeId: this.configForm.itemConfigValue,
          };
          setDefaultTheme(this.configForm.itemConfigValue)
            .then((res) => {
              this.loading = false;
              this.successMsg(this.$t("msg.oper"));
              this.$emit("onSaveDefaultTheme");
              this.onCloseClick();
            })
            .catch(() => {
              this.loading = false;
            });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.setting-drawer-block-checbox {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  margin-top: 10px;
  width: 530px;
}
.config-content {
  display: flex;
  justify-content: center;
}
</style>
<style lang="scss">
.edit-config-dialog {
  .el-dialog__body {
    max-height: 700px;
    overflow-y: auto;
  }
}
</style>
