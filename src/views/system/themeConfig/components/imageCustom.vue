<template>
  <el-upload
    ref="upload"
    class="logoCustom"
    action=""
    :accept="accept"
    :limit="1"
    :multiple="false"
    :auto-upload="false"
    :show-file-list="true"
    :on-remove="onRemoveFile"
    :on-change="onChangeFile"
    :on-exceed="onExceedFile"
    :file-list="fileList"
  >
    <slot></slot>
  </el-upload>
</template>
<script>
export default {
  name: "ImageCustom",
  data() {
    return {
      fileList: [],
    };
  },
  props: {
    accept: {
      type: String,
    },
    name_file: {
      type: String,
      default: "",
    },
    defaultUrl: {
      type: String,
      default: "",
    },
    defaultName: {
      type: String,
      default: "",
    },
  },
  mounted() {
    if (this.defaultUrl) {
      this.fileList = [
        { name: this.defaultName || "logo.svg", url: this.defaultUrl },
      ];
    }
  },
  methods: {
    onChangeFile(file, fileList) {
      let fileNames = file.name.split(".");
      let type = fileNames[fileNames.length - 1];
      // if (!(type == "svg")) {
      //   this.errorMsg(this.$t("themeConfig.fileType_svg"));
      //   this.fileList = [];
      //   return false;
      // }
      this.fileList = fileList;
      this.setIcon(file.raw);
    },
    onRemoveFile(file, fileList) {
      this.fileList = fileList;
      this.setIcon();
    },
    // 超出文件数时弹窗提示
    onExceedFile(files, fileList) {
      let fileNames = files[0].name.split(".");
      let type = fileNames[fileNames.length - 1];
      // if (type != "svg") {
      //   this.errorMsg(this.$t("themeConfig.fileType_svg"));
      //   return false;
      // }
      this.confirmMessage(this.$t("themeConfig.existFile"))
        .then((res) => {
          this.fileList = [];
          this.fileList.push(files[0]);
          this.setIcon(files[0]);
        })
        .catch(() => {});
    },
    setIcon(file) {
      this.$emit("setIcon", this.name_file, file);
    },
  },
};
</script>
<style lang="scss">
.logoCustom {
  .el-upload-list__item {
    background: var(--color-primary19);
    border: 1px dotted var(--color-primary19);
  }
  .el-upload-list__item .el-icon-upload-success {
    color: var(--color-primary19);
  }
}
</style>
