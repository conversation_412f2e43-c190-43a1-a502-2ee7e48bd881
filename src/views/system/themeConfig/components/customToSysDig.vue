<template>
  <div class="custom-to-sys-dig">
    <el-dialog
      :title="$t('themeConfig.saveAsSysTheme')"
      :visible.sync="toSysDigVisible"
      width="700px"
      :modal-append-to-body="true"
      :close-on-click-modal="false"
      :before-close="onCloseClick"
      @open="onOpenDig"
    >
      <div class="dig-content" v-loading="loading">
        <el-form
          @submit.native.prevent
          :model="form"
          :rules="rules"
          label-position="right"
          :label-width="isZh() ? '120px' : '160px'"
          ref="formRef"
        >
          <!-- 中文主题名称 -->
          <el-form-item
            :label="$t('themeConfig.themeNameZh')"
            prop="themeNameZh"
          >
            <el-input
              v-model="form.themeNameZh"
              clearable
              class="w360"
              maxlength="100"
            ></el-input>
          </el-form-item>
          <!-- 英文主题名称 -->
          <el-form-item
            :label="$t('themeConfig.themeNameEn')"
            prop="themeNameEn"
          >
            <el-input
              v-model="form.themeNameEn"
              clearable
              class="w360"
              maxlength="100"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="cancel-btn oper-btn" @click="onCloseClick">{{
          $t("common.cancel")
        }}</el-button>
        <el-button type="primary" class="oper-btn" @click="onSubmitClick">{{
          $t("common.submit")
        }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: "custom-to-sys-dig",
  props: {
    toSysDigVisible: {
      type: Boolean,
      default: false,
    },
    themeAll: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: false,
      form: {
        themeNameZh: "",
        themeNameEn: "",
      },
      rules: {
        themeNameZh: [
          {
            required: true,
            message: this.$t("placeholder.require"),
            trigger: "blur",
          },
        ],
        themeNameEn: [
          {
            required: true,
            message: this.$t("placeholder.require"),
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    onOpenDig() {
      this.form = {
        themeNameZh: "",
        themeNameEn: "",
      };
    },
    onCloseClick() {
      this.$emit("eventClose");
    },
    async onSubmitClick() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.$emit("addThemeNameAndSave", this.form, this.addType);
          this.onCloseClick();
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
<style lang="scss"></style>
