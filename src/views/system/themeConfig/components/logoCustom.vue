<template>
  <div class="sidebar-custom-container">
    <div class="left-container">
      <!-- 图标 -->
      <div class="drawer-item">
        <ImageCustom
          class="upload-demo w360"
          accept="image/svg+xml"
          name_file="logoIcon"
          name_fileName="logoIconName"
          :defaultUrl="logoIcon"
          :defaultName="logoIconName"
          @setIcon="setIcon"
        >
          <el-button size="small" class="w360">{{
            $t("themeConfig.clickUpload_icon")
          }}</el-button>
        </ImageCustom>
      </div>
      <!-- 标题类型 -->
      <el-radio-group v-model="isTitleLogo" class="drawer-item">
        <el-radio :label="false">{{ $t("themeConfig.titleImage") }}</el-radio>
        <el-radio :label="true">{{ $t("themeConfig.titleText") }}</el-radio>
      </el-radio-group>
      <!-- 文字标题区 -->
      <div v-if="isTitleLogo">
        <div class="drawer-item">
          <el-input
            size="small"
            v-model="titleLogo"
            clearable
            class="drawer-item-input"
            maxlength="16"
          ></el-input>
        </div>
        <!-- 是否斜体 -->
        <div class="drawer-item">
          <span>{{ $t("themeConfig.fontStyle") }}</span>
          <el-switch v-model="titleLogoFontStyle" class="drawer-switch" />
        </div>
        <!-- 颜色 -->
        <div class="drawer-item">
          <span>{{ $t("themeConfig.color") }}</span>
          <color-settings
            :isAddClass="false"
            style="float: right; height: 26px; margin: -3px 0px 0 0"
            @setColor="setColor"
            colorName="titleLogoColor"
          ></color-settings>
        </div>
      </div>
      <!-- 图片标题区 -->
      <div class="drawer-item" v-else>
        <!-- <div class="avatar-uploader" @click="uploadPicture">
          <div class="upload-btn">
            <i class="el-icon-plus" style="font-size: 15px" />
          </div>
        </div> -->
        <div class="drawer-item">
          <ImageCustom
            class="upload-demo w360"
            accept="image/svg+xml"
            name_file="imageLogo"
            name_fileName="imageLogoName"
            :defaultUrl="imageLogo"
            :defaultName="imageLogoName"
            @setIcon="setIcon"
          >
            <el-button size="small" class="w360">{{
              $t("themeConfig.clickUpload_title")
            }}</el-button>
          </ImageCustom>
        </div>
      </div>
    </div>
    <!-- 效果展示区 -->
    <div class="right-container">
      <div class="demo-tip">效果预览</div>
      <div
        class="sidebar-logo-demo"
        :style="{
          backgroundColor:
            sideTheme == 'theme-custom'
              ? menuBackgroundCustom
              : sideTheme === 'theme-dark'
              ? variables.menuBackground
              : settings.sideTheme === 'theme-theme'
              ? settings.theme
              : variables.menuLightBackground,
        }"
      >
        <img v-if="logoIcon" :src="logoIcon" class="sidebar-logo" />
        <h1
          v-if="isTitleLogo"
          class="sidebar-title"
          :style="{
            color: titleLogoColor,
            fontSize: '25px',
            fontStyle: titleLogoFontStyle ? 'italic' : 'normal',
          }"
        >
          {{ titleLogo }}
        </h1>
        <img
          v-if="!isTitleLogo && imageLogo"
          :src="imageLogo"
          class="sidebar-logo-name"
        />
      </div>
    </div>

    <!-- 裁切 -->
    <!-- <Cropper
      :cropperModel="cropperModel"
      @eventClose="eventClose"
      @setImage="setImage"
      :cropperWidth="100"
      :cropperHeight="32"
      name="imageLogo"
    ></Cropper> -->
  </div>
</template>

<script>
import titleImg from "@/assets/images/sysLogo.svg";
import variables from "@/assets/styles/variables.scss";
import { mapState, mapGetters } from "vuex";
import ColorSettings from "./colorSettings.vue";
import Cropper from "./cropper.vue";
import ImageCustom from "./imageCustom.vue";
export default {
  name: "LogoCustom",
  components: { ColorSettings, Cropper, ImageCustom },
  data() {
    return {
      logoIcon: this.$store.state.settings.logoIcon,
      logoIconName: this.$store.state.settings.logoIconName,
      imageLogo: this.$store.state.settings.imageLogo,
      imageLogoName: this.$store.state.settings.imageLogoName,
      titleLogoColor: this.$store.state.settings.titleLogoColor,
      // cropperModel: false,  // 裁切区显示
    };
  },
  computed: {
    ...mapState(["settings"]),
    ...mapGetters(["sysImg"]),
    isTitleLogo: {
      get() {
        return this.$store.state.settings.isTitleLogo;
      },
      set(val) {
        this.$emit("setLogo", "isTitleLogo", val);
      },
    },
    titleLogo: {
      get() {
        return this.$store.state.settings.titleLogo;
      },
      set(val) {
        this.$emit("setLogo", "titleLogo", val);
      },
    },
    titleLogoFontStyle: {
      get() {
        return this.$store.state.settings.titleLogoFontStyle;
      },
      set(val) {
        this.$emit("setLogo", "titleLogoFontStyle", val);
      },
    },
    titleImg() {
      return this.$store.state.settings.isCustomTitle
        ? this.$store.state.settings.imageLogo
        : this.sysImg
        ? this.sysImg
        : titleImg;
    },
    variables() {
      return variables;
    },
    sideTheme() {
      return this.$store.state.settings.sideTheme;
    },
    menuBackgroundCustom() {
      return this.$store.state.settings.menuBackgroundCustom;
    },
  },
  watch: {
    fileList: {
      handler(val) {
        console.log(val);
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    // 自定义主题
    setColor(colorName, color) {
      this.$emit("setLogo", colorName, color);
      this.titleLogoColor = color;
    },
    setImage(name, url, blob) {
      this.$emit("setImage", name, url, blob);
      this.titleImg = url;
    },
    setIcon(file, fileName, name_file, name_fileName) {
      if (file) {
        let img = new FileReader();
        img.readAsDataURL(file);
        img.onload = ({ target }) => {
          this.logoIcon = target.result; //将img转化为二进制数据
          this.logoIconName = fileName;
          this.$emit("setLogo", name_file, this.logoIcon);
          this.$emit("setLogo", name_fileName, this.logoIconName);
        };
      } else {
        this.logoIcon = "";
        this.fileName = "";
        this.$emit("setLogo", name_file, "");
        this.$emit("setLogo", name_fileName, "");
      }
    },
    // 图片裁切区-显示
    // uploadPicture() {
    //   this.cropperModel = true;
    // },
    // eventClose() {
    //   this.cropperModel = false;
    // },
  },
};
</script>

<style lang="scss" scoped>
.sidebar-logo-demo {
  box-shadow: 1px 2px 2px 3px #e6e6e6;
  width: 224px;
  height: 56px;
  text-align: center;
  line-height: 55px;
  background-color: #ccc;
  overflow: hidden;
  .sidebar-logo {
    width: 32px;
    height: 32px;
    vertical-align: middle;
    margin-right: 12px;
  }
  .sidebar-logo-name {
    display: inline-block;
    height: 32px;
    width: 100px;
    vertical-align: middle;
  }
  .sidebar-title {
    display: inline-block;
    margin: 0;
    font-weight: 900;
    line-height: 56px;
    font-size: 25px;
    font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
    vertical-align: middle;
  }
}
.sidebar-custom-container {
  display: flex;
  .left-container {
    height: 100%;
    font-size: 14px;
    line-height: 1.5;
    word-wrap: break-word;

    .drawer-item {
      width: 360px;
      color: rgba(0, 0, 0, 0.65);
      font-size: 14px;
      padding: 12px 0;
    }
    .drawer-item-title-container {
      width: 360px;
      height: 40px;
      line-height: 40px;
      vertical-align: middle;
      .drawer-item-label {
        width: 80px;
      }
      .drawer-item-input {
        width: 360px;
      }
    }

    .drawer-switch {
      float: right;
    }
  }
  .right-container {
    margin: 15px 0 0 40px;
  }
}
</style>
<!-- <style lang="scss" scoped>
.avatar-uploader {
  cursor: pointer;
  width: 100px;
  height: 100px;
  .el-upload,
  .upload-btn {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    position: relative;
    overflow: hidden;
  }
}
.avatar-uploader .el-upload:hover {
  border-color: var(--color-primary);
}
.avatar-uploader-icon,
.el-icon-plus {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
</style> -->
