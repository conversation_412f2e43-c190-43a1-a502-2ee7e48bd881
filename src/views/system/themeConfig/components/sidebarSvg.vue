<template>
  <el-tooltip
    class="item"
    effect="dark"
    placement="bottom-start"
    popper-class="settings-tip-class"
  >
    <div slot="content" style="text-align: center">
      {{ isZh() ? themeData.themeNameZh : themeData.themeNameEn }}
      <!-- 删除主题 接口/本地 -->
      <el-button
        type="text"
        class="oper-text-btn theme-delete-btn"
        v-if="themeData.isDelete"
        @click="onDeleteClick(true)"
        >{{ $t("common.delete") }}</el-button
      >
      <div
        style="text-align: center; margin-top: 5px; min-width: 100px"
        v-if="defaultThemeId == themeData.themeId"
      >
        ({{ $t("themeConfig.systemDefaultTopic") }})
      </div>
      <!-- <el-button
        type="text"
        class="oper-text-btn theme-delete-btn"
        v-if="themeData.isDelete"
        @click="onDeleteClick(false)"
        >{{ $t("common.delete") }}</el-button
      > -->
    </div>
    <div class="setting-drawer-block-checbox-item" @click="handleTheme">
      <svg
        width="52px"
        height="45px"
        viewBox="0 0 52 45"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
      >
        <defs>
          <filter
            x="-9.4%"
            y="-6.2%"
            width="118.8%"
            height="122.5%"
            filterUnits="objectBoundingBox"
            id="filter-1"
          >
            <feOffset
              dx="0"
              dy="1"
              in="SourceAlpha"
              result="shadowOffsetOuter1"
            ></feOffset>
            <feGaussianBlur
              stdDeviation="1"
              in="shadowOffsetOuter1"
              result="shadowBlurOuter1"
            ></feGaussianBlur>
            <feColorMatrix
              values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.15 0"
              type="matrix"
              in="shadowBlurOuter1"
              result="shadowMatrixOuter1"
            ></feColorMatrix>
            <feMerge>
              <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
              <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
          </filter>
          <rect id="path-2" x="0" y="0" width="48" height="40" rx="4"></rect>
          <filter
            x="-4.2%"
            y="-2.5%"
            width="108.3%"
            height="110.0%"
            filterUnits="objectBoundingBox"
            id="filter-4"
          >
            <feOffset
              dx="0"
              dy="1"
              in="SourceAlpha"
              result="shadowOffsetOuter1"
            ></feOffset>
            <feGaussianBlur
              stdDeviation="0.5"
              in="shadowOffsetOuter1"
              result="shadowBlurOuter1"
            ></feGaussianBlur>
            <feColorMatrix
              values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.1 0"
              type="matrix"
              in="shadowBlurOuter1"
            ></feColorMatrix>
          </filter>
        </defs>
        <g
          id="配置面板"
          stroke="none"
          stroke-width="1"
          fill="none"
          fill-rule="evenodd"
        >
          <g
            id="setting-copy-2"
            transform="translate(-1254.000000, -136.000000)"
          >
            <g id="Group-8" transform="translate(1167.000000, 0.000000)">
              <g
                id="Group-5"
                filter="url(#filter-1)"
                transform="translate(89.000000, 137.000000)"
              >
                <mask id="mask-3" fill="white">
                  <use xlink:href="#path-2"></use>
                </mask>
                <g id="Rectangle-18">
                  <use
                    fill="black"
                    fill-opacity="1"
                    filter="url(#filter-4)"
                    xlink:href="#path-2"
                  ></use>
                  <use
                    fill="#F0F2F5"
                    fill-rule="evenodd"
                    xlink:href="#path-2"
                  ></use>
                </g>
                <rect
                  id="Rectangle-11"
                  :fill="themeData.topColor"
                  mask="url(#mask-3)"
                  x="0"
                  y="0"
                  width="48"
                  height="10"
                ></rect>
                <rect
                  :fill="themeData.leftColor"
                  mask="url(#mask-3)"
                  x="0"
                  y="0"
                  width="16"
                  height="10"
                ></rect>
                <rect
                  :fill="themeData.leftColorActive"
                  mask="url(#mask-3)"
                  x="0"
                  y="10"
                  width="16"
                  height="5"
                ></rect>
                <rect
                  :fill="themeData.leftColor"
                  mask="url(#mask-3)"
                  x="0"
                  y="15"
                  width="16"
                  height="25"
                ></rect>
              </g>
            </g>
          </g>
        </g>
      </svg>
      <div
        v-if="currentThemeId == themeData.themeId"
        class="setting-drawer-block-checbox-selectIcon"
        style="display: block"
      >
        <i aria-label="图标: check" class="anticon anticon-check">
          <svg
            viewBox="64 64 896 896"
            data-icon="check"
            width="1em"
            height="1em"
            :fill="theme"
            aria-hidden="true"
            focusable="false"
            class=""
          >
            <path
              d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
            />
          </svg>
        </i>
      </div>
      <div class="new-text" v-if="themeData.isNew">New</div>
      <div class="item-themeName" v-if="isZh()">
        {{ themeData.themeNameZh }}
      </div>
      <div class="item-themeName" v-else>
        {{ themeData.themeNameEn }}
      </div>
    </div>
  </el-tooltip>
</template>
<script>
export default {
  name: "SidebarSvg",
  props: {
    themeData: {
      default: () => {},
      type: Object,
    },
    currentThemeId: {
      type: String,
      default: "",
    },
    defaultThemeId: {
      type: String,
      default: "",
    },
    theme: {
      type: String,
    },
    sideTheme: {
      type: String,
    },
    isCustom: {
      type: Boolean,
      default: false,
    },
  },
  mounted() {},
  methods: {
    handleTheme() {
      this.$emit("handleTheme", this.themeData);
    },
    onDeleteClick(type) {
      this.confirmMessage(this.$t("themeConfig.deleteThemeTip"))
        .then((res) => {
          this.$emit("deleteTheme", this.themeData, type);
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="scss" scoped>
.setting-drawer-block-checbox {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 10px;
  .setting-drawer-block-checbox-item {
    position: relative;
    margin-right: 30px;
    margin-bottom: 60px;
    border-radius: 2px;
    cursor: pointer;
    // width: 100px;
    height: 45px;
    text-align: center;
    .item-themeName {
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 14px;
      line-height: 1.5;
    }
    .new-text {
      position: absolute;
      top: -5px;
      left: 2px;
      height: 14px;
      line-height: 14px;
      background: #40b450;
      color: #fff;
      font-size: 10px;
      padding: 0 2px;
      border-radius: 2px;
    }
    .setting-drawer-block-checbox-selectIcon {
      position: absolute;
      top: 0;
      right: 0;
      width: 100%;
      height: 100%;
      padding-top: 15px;
      padding-left: 15px;
      color: #1890ff;
      font-weight: 700;
      font-size: 14px;
      line-height: 1;
      .theme-plus {
        color: var(--color-primary);
        font-weight: 700;
      }
    }
  }
}
</style>
<style lang="scss">
.settings-tip-class {
  padding: 10px 10px !important;
  .theme-delete-btn {
    padding: 0;
    color: #f5222d;
  }
}
</style>
