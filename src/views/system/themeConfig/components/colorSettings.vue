<template>
  <el-color-picker
    v-model="color"
    :show-alpha="showAlpha"
    :predefine="[
      '#1C6CDD',
      '#775C9F',
      '#00203f',
      '#304156',
      '#212121',
      '#11a983',
      '#13c2c2',
      '#6959CD',
      '#f5222d',
    ]"
    class="theme-picker"
    popper-class="theme-picker-dropdown"
  />
</template>
<script>
export default {
  data() {
    return {
      color: "",
    };
  },
  props: {
    colorName: {
      default: "#fff",
      type: String,
    },
    showAlpha: {
      default: false,
      type: Boolean,
    },
    isAddClass: {
      default: false,
      type: Boolean,
    },
  },
  computed: {
    defaultColor() {
      return this.$store.state.settings[this.colorName];
    },
  },
  watch: {
    defaultColor: {
      handler: function (val, oldVal) {
        this.color = val;
      },
      immediate: true,
    },
    async color(val) {
      await this.setColor(val);
    },
  },
  methods: {
    setColor(color) {
      console.log(color);
      this.$emit("setColor", this.colorName, color, this.isAddClass);
    },
  },
};
</script>
