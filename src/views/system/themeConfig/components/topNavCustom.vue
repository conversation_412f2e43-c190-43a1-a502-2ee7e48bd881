<template>
  <div class="topNav-custom-container">
    <div class="left-container">
      <div class="drawer-item">
        <span>背景色</span>
        <color-settings
          :isAddClass="false"
          style="float: right; height: 26px; margin: -3px 0px 0 0"
          @setColor="setColor"
          colorName="topBackgroundCustom"
        ></color-settings>
      </div>
      <div class="drawer-item">
        <span>svg颜色</span>
        <color-settings
          :isAddClass="false"
          style="float: right; height: 26px; margin: -3px 0px 0 0"
          @setColor="setColor"
          colorName="topSvgCustom"
        ></color-settings>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import ColorSettings from "./colorSettings.vue";
export default {
  name: "TopNavCustom",
  components: { ColorSettings },
  computed: {
    ...mapState(["settings"]),
  },
  methods: {
    // 自定义主题
    setColor(colorName, color) {
      this.$emit("setColor", colorName, color);
    },
  },
};
</script>

<style lang="scss" scoped>
.topNav-custom-container {
  display: flex;
  .left-container {
    height: 100%;
    font-size: 14px;
    line-height: 1.5;
    word-wrap: break-word;

    .drawer-item {
      width: 300px;
      color: rgba(0, 0, 0, 0.65);
      font-size: 14px;
      margin: 12px 0;
      height: 38px;
      line-height: 38px;
    }

    .drawer-switch {
      float: right;
    }
  }
  .right-container {
    margin: 40px 0 0 40px;
  }
}
</style>
<style lang="scss">
// 自定义主题-左侧菜单demo
.topNav-custom-container {
  .theme-custom .nest-menu .el-submenu > .el-submenu__title,
  .theme-custom .el-submenu .el-menu-item {
    background-color: var(--custom-sub-menu-background) !important;

    &:hover {
      background-color: var(--custom-sub-menu-hover) !important;
    }
  }

  .theme-custom .is-active {
    background-color: var(--custom-sub-menu-background-active) !important;
    color: var(--custom-menu-color-active) !important;
  }

  .theme-custom .is-opened .is-active {
    background-color: var(--custom-sub-menu-background-active) !important;
    color: var(--custom-menu-color-active) !important;
  }
}
</style>
