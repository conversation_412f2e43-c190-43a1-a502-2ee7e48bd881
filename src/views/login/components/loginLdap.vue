<template>
  <div class="login-user" v-loading="loading">
    <el-form
      @submit.native.prevent
      class="user-form"
      :model="userForm"
      ref="userFormRef"
      :rules="rules"
    >
      <el-form-item prop="ldapUserDn">
        <el-input
          v-model="userForm.ldapUserDn"
          maxlength="64"
          :placeholder="$t('placeholder.ldapUserName')"
        ></el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="userForm.password"
          type="password"
          :placeholder="$t('placeholder.ldapPassword')"
          @keyup.enter.native="onLoginUserClick"
        ></el-input>
      </el-form-item>
    </el-form>
    <el-button
      type="primary"
      class="login-btn"
      :loading="btnLoading"
      @click="onLoginUserClick"
      >{{ $t("common.login") }}</el-button
    >
    <div class="login-tip">
      <div>注：登录账户同您的办公电脑登录账户一致，初次登录后</div>
      <div>会和当前门户绑定，后续可联系管理员变更门户</div>
    </div>
  </div>
</template>
<script>
import { getUserStatus } from "@/api/login";
import { loginLdap } from "@/api/login";
export default {
  name: "login-ldap",
  props: {
    redirect: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      userForm: {
        ldapUserDn: "",
        password: "",
      },
      codeImg: "",
      rules: {
        ldapUserDn: [
          {
            required: true,
            message: this.$t("placeholder.ldapUserName"),
            trigger: "blur",
          },
        ],
        password: [
          {
            required: true,
            message: this.$t("placeholder.ldapPassword"),
            trigger: "blur",
          },
        ],
      },
      requestId: "",
      loading: false,
      btnLoading: false,
    };
  },
  methods: {
    onLoginUserClick() {
      this.$refs.userFormRef.validate(async (valid) => {
        if (valid) {
          this.btnLoading = true;
          let params = {
            userName: this.userForm.ldapUserDn.trim(),
            userPwd: Base64.encode(this.userForm.password),
            // ldapAuth: this.$gm.encodeSM2(this.userForm.password),
            requestId: this.requestId,
            // ldapUserDn: this.userForm.ldapUserDn.trim(),
            // captcha: this.userForm.verificationCode,
            // token: this.secondData.firstToken,
            type: "ldap",
          };
          this.$store
            .dispatch("Login", params)
            .then((res) => {
              this.btnLoading = false;
              this.$router.push({ path: this.redirect || "/" }).catch(() => {});
            })
            .catch((error) => {
              this.btnLoading = false;
              if(error.code === "00060A17"){
                //提示跳转到对应门户
                this.confirmMessage(error.message)
                  .then(() => {
                    window.location.href = error.result;
                  });
              }
            });
        } else {
          return false;
        }
      });
    },
  },
  mounted() {},
};
</script>
<style lang="scss">
.login-user {
  height: 100%;
  .login-tip {
    color: #666;
    font-size: 14px;
    text-align: right;
    margin-top: 16px;
    line-height: 1.4;
    div {
      margin-bottom: 4px;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  .user-form {
    height: calc(100% - 48px);
  }
  .el-form-item .el-form-item__content .el-input__inner {
    height: 48px;
    line-height: 48px;
  }
  .img-box {
    width: 116px;
    height: 48px;
    border: 1px solid #888;
    margin-left: 24px;
    border-radius: 4px;
    cursor: pointer;
  }
  .code-box {
    display: flex;
    align-items: center;
  }
}
</style>
