<template>
  <div class="login-user" v-loading="loading">
    <el-form
      @submit.native.prevent
      class="user-form"
      :model="userForm"
      ref="userFormRef"
      :rules="rules"
    >
      <el-form-item prop="username">
        <el-input
          v-model="userForm.username"
          maxlength="64"
          :placeholder="$t('placeholder.username')"
          @change="onUserNameChange"
        ></el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="userForm.password"
          type="password"
          :placeholder="$t('placeholder.password')"
          @keyup.enter.native="onLoginUserClick"
          maxlength="64"
        ></el-input>
      </el-form-item>
      <el-form-item prop="verificationCode" v-if="isShowVerCode">
        <div class="code-box">
          <el-input
            v-model="userForm.verificationCode"
            class="w240"
            :placeholder="$t('login.verificationCode')"
            @keyup.enter.native="onLoginUserClick"
            maxlength="10"
          ></el-input>
          <img class="w116 img-box" :src="codeImg" alt="" @click="getCaptcha" />
        </div>
      </el-form-item>
    </el-form>
    <el-button
      type="primary"
      class="login-btn"
      :loading="btnLoading"
      @click="onLoginUserClick"
      >{{ $t("common.login") }}</el-button
    >
  </div>
</template>
<script>
import { getCodeImg, judgeVerificationCode, getUserStatus } from "@/api/login";
export default {
  name: "login-user",
  props: {
    redirect: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      userForm: {
        username: "",
        password: "",
        captchaId: "",
        verificationCode: "",
      },
      codeImg: "",
      rules: {
        username: [
          {
            required: true,
            message: this.$t("placeholder.username"),
            trigger: "blur",
          },
        ],
        password: [
          {
            required: true,
            message: this.$t("placeholder.password"),
            trigger: "blur",
          },
        ],
        verificationCode: [
          {
            required: true,
            message: this.$t("placeholder.verificationCode"),
            trigger: "blur",
          },
        ],
      },
      loading: false,
      btnLoading: false,
      isShowVerCode: false,
    };
  },
  methods: {
    // 获取验证码
    getCaptcha() {
      getCodeImg()
        .then((res) => {
          this.loading = false;
          this.userForm.captchaId = res.data.captchaId;
          this.codeImg = res.data.captchaBase64;
        })
        .catch(() => {
          this.loading = false;
        });
    },

    // 用户聚焦保留原来值
    onUserNameChange() {
      if (this.userForm.username) {
        this.userForm.verificationCode = "";
        this.getVerCode();
      }
    },
    async getVerCode() {
      let res = await getUserStatus({
        userName: this.userForm.username,
      });
      this.isShowVerCode = res.data.codeDisplay == 1; // 0不需要验证码
      this.isBase64 = res.data.encryType == 1; // 2不需要base64
      if (this.isShowVerCode) {
        this.getCaptcha();
      }
    },
    onLoginUserClick() {
      this.$refs.userFormRef.validate(async (valid) => {
        if (valid) {
          // 如果未展示验证码，需要校验是否展示验证码，防止多次点击
          if (!this.isShowVerCode) {
            await this.getVerCode();
          }
          if (this.isShowVerCode && !this.userForm.verificationCode) {
            return false;
          }
          this.btnLoading = true;
          let params = {
            // psd: Base64.encode(this.userForm.password),
            userPwd: this.$sm3(this.userForm.password),
            userName: this.userForm.username.trim(),
            captcha: this.userForm.verificationCode,
            captchaId: this.userForm.captchaId,
            type: "user",
          };
          this.$store
            .dispatch("Login", params)
            .then(() => {
              this.btnLoading = false;
              this.$router.push({ path: this.redirect || "/" }).catch(() => {});
            })
            .catch(() => {
              this.btnLoading = false;
              if (this.isShowVerCode) {
                this.userForm.verificationCode = "";
                this.getCaptcha();
              }
            });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.login-user {
  height: 100%;
  .user-form {
    height: calc(100% - 48px);
  }
  .el-form-item .el-form-item__content .el-input__inner {
    height: 48px;
    line-height: 48px;
  }
  .img-box {
    width: 116px;
    height: 48px;
    border: 1px solid #888;
    margin-left: 24px;
    border-radius: 4px;
    cursor: pointer;
  }
  .code-box {
    display: flex;
    align-items: center;
  }
}
</style>
