<template>
  <div class="icons-container full-height">
    <el-tabs type="border-card">
      <el-tab-pane label="Svg Icons">
        <div v-for="item of svgIcons" :key="item">
          <el-tooltip placement="top">
            <div slot="content">
              {{ generateIconCode(item) }}
            </div>
            <div
              class="icon-item"
              v-clipboard:copy="generateIconCode(item)"
              v-clipboard:success="onCopySucc"
            >
              <svg-icon :icon-class="item" class-name="disabled" />
              <span>{{ item }}</span>
            </div>
          </el-tooltip>
        </div>
      </el-tab-pane>
      <el-tab-pane label="Element-UI Icons">
        <div v-for="item of elementIcons" :key="item">
          <el-tooltip placement="top">
            <div slot="content">
              {{ generateElementIconCode(item) }}
            </div>
            <div
              class="icon-item"
              v-clipboard:copy="generateElementIconCode(item)"
              v-clipboard:success="onCopySucc"
            >
              <i :class="'el-icon-' + item" />
              <span>{{ item }}</span>
            </div>
          </el-tooltip>
        </div>
      </el-tab-pane>
      <el-tab-pane label="Iconfont Icons">
        <div v-for="item of iconfontIcons" :key="item">
          <el-tooltip placement="top">
            <div slot="content">
              {{ generateIconfontIconCode(item) }}
            </div>
            <div
              class="icon-item iconfont-item"
              v-clipboard:copy="generateIconfontIconCode(item)"
              v-clipboard:success="onCopySucc"
            >
              <i class="iconfont" :class="item" />
              <span>{{ item }}</span>
            </div>
          </el-tooltip>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import svgIcons from "./svg-icons";
import elementIcons from "./element-icons";
import iconfontIcons from "./iconfont-icons";

export default {
  name: "Icons",
  data() {
    return {
      svgIcons,
      elementIcons,
      iconfontIcons,
    };
  },
  methods: {
    generateIconCode(symbol) {
      return `<svg-icon icon-class="${symbol}" />`;
    },
    generateElementIconCode(symbol) {
      return `<i class="el-icon-${symbol}" />`;
    },
    generateIconfontIconCode(symbol) {
      return `<i class="iconfont ${symbol}" />`;
    },
    onCopySucc() {
      this.successMsg(this.$t("msg.copy"));
    },
  },
};
</script>

<style lang="scss" scoped>
.icons-container {
  // margin: 10px 20px 0;
  overflow: hidden;
  overflow-y: auto;
  .svg-icon.disabled {
    width: 30px;
    height: 30px;
  }

  .icon-item {
    margin: 20px;
    height: 85px;
    text-align: center;
    width: 80px;
    float: left;
    font-size: 30px;
    color: #24292e;
    cursor: pointer;
    &:hover {
      background: #eee;
    }
  }
  .iconfont-item {
    word-break: break-all;
    i {
      font-size: 30px;
    }
  }

  span {
    display: block;
    font-size: 16px;
    margin-top: 10px;
  }

  .disabled {
    pointer-events: none;
  }
}
</style>
