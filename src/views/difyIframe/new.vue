<template>
  <div class="full-height" style="display: flex; flex-direction: column">
    <div class="comm-page-title">
      <page-header>
        <div class="app-header" style="display: flex">
          <div class="app-icon">
            <div
              class="emoji-icon"
              :style="{ background: appInfo.icon_background }"
              v-if="appInfo.icon_type == 'emoji'"
            >
              <emoji
                :data="emojiData"
                :emoji="appInfo.icon"
                :native="true"
                :size="20"
              />
              <!-- {{ appInfo.icon }} -->
            </div>
            <img v-else :src="appInfo.icon_url" object-fit="contain" />
          </div>
          <div class="app-right">
            <div class="app-title" style="font-weight: bold">
              {{ appInfo.name }}
            </div>
          </div>
        </div>
      </page-header>
    </div>
    <i-frame v-if="url" :src="url" style="flex: 1" id="bdIframe" />
  </div>
</template>
<script>
import iFrame from "@/components/iFrame/index";
import { getAppDifyUrl, getAppDetail } from "@/api/ai/app";
import data from "emoji-mart-vue-fast/data/all.json";
import { Emoji, EmojiIndex } from "emoji-mart-vue-fast";
const emojiData = new EmojiIndex(data);
export default {
  name: "IframeCom",
  components: { iFrame, Emoji },
  data() {
    return {
      emojiData,
      url: "",
      isFixed: false,
      appInfo: {},
      appId: "",
    };
  },
  created() {
    this.refreshData();
  },
  methods: {
    refreshData() {
      let appId = this.$route.query.appId;
      this.appId = appId;
      this.getAppInfo();
      this.getAppUrl();
    },
    getAppUrl() {
      getAppDifyUrl({ appId: this.appId }).then((res) => {
        this.url = "";
        this.$nextTick(() => {
          this.url = res.data;
        });
      });
    },
    getAppInfo() {
      if (this.appId == this.appInfo["id"]) {
        return;
      }
      getAppDetail({ appId: this.appId }).then((res) => {
        this.appInfo = res.data;
      });
    },
  },
  watch: {
    $route(val) {
      this.refreshData();
    },
  },
};
</script>
<style lang="scss" scoped>
.app-header {
  display: flex;
  line-height: 32px;
  .app-icon {
    width: 32px;
    margin-right: 10px;
    text-align: center;
    .emoji-icon {
      border-radius: 4px;
    }
    img {
      width: 100%;
      border-radius: 4px;
    }
  }
}
</style>
