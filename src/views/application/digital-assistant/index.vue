<template>
  <div class="digital-translate-container">
    <div class="digital-content">
      <i-frame src="/digital-assistant" style="flex: 1" id="bdIframe" />
    </div>
  </div>
</template>

<script>
import iFrame from "@/components/iFrame/index.vue";

export default {
  name: "DigitalAssistant",
  components: { iFrame },
  computed: {
  },
};
</script>

<style lang="scss" scoped>
.digital-translate-container {

  .digital-content {
    .upload-card, .preview-card {
      height: 600px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: bold;
      }
    }

    .upload-area {
      margin-bottom: 20px;

      .upload-dragger {
        width: 100%;
      }
    }

    .translate-settings {
      margin-top: 20px;
    }

    .preview-content {
      height: 500px;

      .empty-preview {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #999;

        i {
          font-size: 48px;
          margin-bottom: 16px;
        }
      }

      .translate-result {
        height: 100%;
        display: flex;
        flex-direction: column;

        .progress-info {
          text-align: center;
          padding: 20px;

          .progress-text {
            margin-top: 10px;
            color: #666;
          }
        }

        .result-content {
          flex: 1;
          display: flex;
          flex-direction: column;

          .page-navigation {
            text-align: center;
            margin-bottom: 20px;
          }

          .page-content {
            flex: 1;
            overflow-y: auto;

            .original-text, .translated-text {
              h4 {
                margin: 0 0 10px 0;
                color: #333;
              }

              .text-content {
                padding: 15px;
                background: #f5f5f5;
                border-radius: 4px;
                line-height: 1.6;
                min-height: 100px;
                white-space: pre-wrap;
              }
            }
          }
        }
      }
    }
  }
}
</style>
