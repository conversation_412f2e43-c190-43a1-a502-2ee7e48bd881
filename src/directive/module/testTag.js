/**
 * 给自动化测试添加标记
 */
export default {
  inserted(el, binding, vnode) {
    if (vnode.componentOptions.tag == "el-form-item") {
      let props = vnode.componentOptions.propsData;
      let children = vnode.componentOptions.children;
      if (children.length > 0) {
        if (props["prop"]) {
          let id = `${props["prop"]}${el.getAttribute("area")}`;
          let formItem = children[0].componentOptions;
          let input = null;
          if (formItem) {
            switch (formItem.tag) {
              case "el-input":
                input = el.querySelector(".el-input__inner");
                if (!input) {
                  input = el.querySelector(".el-textarea__inner");
                }

                // 输入框限制输入长度，搜索条件限制256，表单默认限制20000
                let maxlength =
                  input.getAttribute("maxlength") ||
                  (el.getAttribute("area") == "SearchId" ? "256" : "20000");
                input.setAttribute("maxlength", maxlength);
                break;
              case "el-select":
                input = el.querySelector(".el-input__inner");
                if (!input) {
                  input = el.querySelector(".el-textarea__inner");
                }
                break;
              case "el-radio-group":
                input = el.querySelector(".el-radio-group");
                break;
            }
            if (input && !input.hasAttribute("id")) {
              // input.id = id;
              input.setAttribute("id", id);
            }
          }
        }
      }
    }
  },
};
