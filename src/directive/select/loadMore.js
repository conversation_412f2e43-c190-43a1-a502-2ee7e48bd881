import { debounce } from "lodash";

export default {
  inserted(el, binding) {
    const dom = el.querySelector(
      ".el-select-dropdown .el-select-dropdown__wrap",
    );
    const handle = debounce((e) => {
      let scrollDistance = dom.scrollHeight - dom.scrollTop;
      // 预留10px位置用于触底
      if (dom.clientHeight + 10 > scrollDistance) {
        binding.value();
      }
    }, 170);
    dom?.addEventListener("scroll", handle);
    // 方法挂载到元素身上便于解绑时使用
    el._handle = handle;
  },
  unbind(el, binding) {
    const dom = el.querySelector(
      ".el-select-dropdown .el-select-dropdown__wrap",
    );
    dom?.removeEventListener("scroll", el._handle);
    el._handle = null;
  },
};
