{"name": "portal", "version": "3.8.2", "description": "门户管理系统", "author": "", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:prodIframe": "vue-cli-service build --mode productionIframe", "preview": "node build/index.js --preview", "ssh": "node D:/git/doc/kms-deploy-sshtar.js -dic SecPortal -sshDic opt/sansec/web -sshIps ************ -sshUser root -sshPwd SansecAI@2025", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "dependencies": {"@babel/eslint-parser": "^7.25.1", "@riophae/vue-treeselect": "0.4.0", "axios": "0.24.0", "clipboard": "2.0.8", "core-js": "3.19.1", "cross-env": "^7.0.3", "crypto": "^1.0.1", "crypto-js": "^3.3.0", "dingtalk-jsapi": "^3.1.1", "echarts": "^5.3.3", "element-ui": "2.15.8", "emoji-mart-vue-fast": "^15.0.4", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.30.0", "file-saver": "2.0.5", "fuse.js": "6.4.3", "gm-crypto": "^0.1.8", "js-base64": "^2.6.2", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "json-bigint": "^1.0.0", "lodash": "^4.17.15", "nprogress": "0.2.0", "prism-themes": "^1.9.0", "prismjs": "^1.29.0", "qs": "^6.11.2", "quill": "1.3.7", "resize-detector": "^0.3.0", "screenfull": "5.0.2", "sm-crypto": "^0.3.12", "sortablejs": "1.10.2", "spark-md5": "^3.0.2", "uuid": "^8.3.2", "vue": "^2.6.13", "vue-clipboard2": "^0.3.3", "vue-count-to": "1.0.13", "vue-grid-layout": "^2.4.0", "vue-i18n": "7.3.2", "vue-jsonp": "^2.0.0", "vue-meta": "2.4.0", "vue-router": "3.4.9", "vue-virtual-scroll-list": "^2.3.5", "vuedraggable": "2.24.3", "vuex": "3.6.0", "webpack": "4.47.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "5.0.8", "@vue/cli-service": "4.4.6", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "4.1.0", "code-inspector-plugin": "^0.15.2", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "8.0.0", "eslint-plugin-vue": "8.0.3", "html-webpack-plugin": "3.2.0", "lint-staged": "10.5.3", "mockjs2": "^1.0.8", "runjs": "4.4.2", "rxjs": "^6.5.3", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-cropper": "^0.5.8", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}